{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/dev/bubblekrc/cryptobubble-clone/src/components/BubbleChart.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useEffect, useRef, useState } from \"react\";\nimport * as d3 from \"d3\";\nimport { CryptoCurrency, BubbleData } from \"@/types\";\n\ninterface BubbleChartProps {\n  data: CryptoCurrency[];\n  width?: number;\n  height?: number;\n  onBubbleHover?: (crypto: CryptoCurrency | null) => void;\n}\n\nexport const BubbleChart: React.FC<BubbleChartProps> = ({\n  data,\n  width = 1200,\n  height = 800,\n  onBubbleHover,\n}) => {\n  const svgRef = useRef<SVGSVGElement>(null);\n  const [hoveredBubble, setHoveredBubble] = useState<CryptoCurrency | null>(\n    null,\n  );\n  const [pinnedBubble, setPinnedBubble] = useState<CryptoCurrency | null>(\n    null,\n  );\n\n  useEffect(() => {\n    if (!svgRef.current || !data.length) return;\n\n    const svg = d3.select(svgRef.current);\n    svg.selectAll(\"*\").remove();\n\n    // Create scales\n    const maxMarketCap = d3.max(data, (d) => d.marketCap) || 1;\n    const minMarketCap = d3.min(data, (d) => d.marketCap) || 1;\n\n    // Calculate optimal bubble sizes to fill screen without overlapping\n    const totalArea = width * height;\n    const bubbleCount = data.length;\n    const averageArea = (totalArea / bubbleCount) * 0.5; // Use 50% of available space for non-overlapping\n    const averageRadius = Math.sqrt(averageArea / Math.PI);\n\n    // Radius scale - conservative sizing to prevent overlap\n    const radiusScale = d3\n      .scaleSqrt()\n      .domain([minMarketCap, maxMarketCap])\n      .range([averageRadius * 0.4, averageRadius * 1.8]); // More conservative sizing\n\n    // Color scale for price changes with more vibrant colors\n    const colorScale = d3\n      .scaleLinear<string>()\n      .domain([-15, -5, 0, 5, 15])\n      .range([\"#dc2626\", \"#f87171\", \"#6b7280\", \"#34d399\", \"#059669\"])\n      .clamp(true);\n\n    // Prepare bubble data\n    const bubbleData: BubbleData[] = data.map((crypto) => ({\n      ...crypto,\n      x: 0,\n      y: 0,\n      r: radiusScale(crypto.marketCap),\n      color: colorScale(crypto.change24h),\n    }));\n\n    // Create force simulation for non-overlapping bubbles like CryptoBubbles\n    const simulation = d3\n      .forceSimulation<BubbleData>(bubbleData)\n      .force(\"charge\", d3.forceManyBody().strength(-50)) // Moderate repulsion to prevent overlap\n      .force(\"center\", d3.forceCenter(width / 2, height / 2))\n      .force(\n        \"collision\",\n        d3\n          .forceCollide<BubbleData>()\n          .radius((d) => d.r + 3) // Small padding to prevent overlap\n          .strength(1) // Strong collision detection\n          .iterations(3),\n      ) // Multiple iterations for better collision resolution\n      .force(\"x\", d3.forceX<BubbleData>(width / 2).strength(0.05)) // Gentle centering\n      .force(\"y\", d3.forceY<BubbleData>(height / 2).strength(0.05))\n      // Add boundary forces to keep bubbles on screen\n      .force(\"boundary\", () => {\n        bubbleData.forEach((d) => {\n          const padding = d.r + 5;\n          d.x = Math.max(padding, Math.min(width - padding, d.x || width / 2));\n          d.y = Math.max(\n            padding,\n            Math.min(height - padding, d.y || height / 2),\n          );\n        });\n      })\n      .alphaDecay(0.005) // Very slow decay for thorough settling\n      .velocityDecay(0.6); // Higher friction for stability\n\n    // Create container group\n    const container = svg.append(\"g\");\n\n    // Create bubbles with entrance animation\n    const bubbles = container\n      .selectAll(\".bubble\")\n      .data(bubbleData)\n      .enter()\n      .append(\"g\")\n      .attr(\"class\", \"bubble\")\n      .style(\"cursor\", \"pointer\")\n      .style(\"opacity\", 0);\n\n    // Add circles with gradient effects and inner glow\n    const defs = svg.append(\"defs\");\n\n    // Create striking inner glow filter using bubble's own color\n    const innerGlowFilter = defs\n      .append(\"filter\")\n      .attr(\"id\", \"inner-glow\")\n      .attr(\"x\", \"-50%\")\n      .attr(\"y\", \"-50%\")\n      .attr(\"width\", \"200%\")\n      .attr(\"height\", \"200%\");\n\n    // Step 1: Create inverted alpha mask\n    innerGlowFilter\n      .append(\"feComposite\")\n      .attr(\"in\", \"SourceAlpha\")\n      .attr(\"in2\", \"SourceAlpha\")\n      .attr(\"operator\", \"out\")\n      .attr(\"result\", \"inverse\");\n\n    // Step 2: Blur the inverted mask for glow effect\n    innerGlowFilter\n      .append(\"feGaussianBlur\")\n      .attr(\"in\", \"inverse\")\n      .attr(\"stdDeviation\", \"5\")\n      .attr(\"result\", \"blurred\");\n\n    // Step 3: Clip the blur to the original shape for inner glow\n    innerGlowFilter\n      .append(\"feComposite\")\n      .attr(\"in\", \"blurred\")\n      .attr(\"in2\", \"SourceAlpha\")\n      .attr(\"operator\", \"in\")\n      .attr(\"result\", \"innerGlow\");\n\n    // Step 4: Combine with original for striking effect\n    const feMerge = innerGlowFilter.append(\"feMerge\");\n    feMerge.append(\"feMergeNode\").attr(\"in\", \"SourceGraphic\");\n    feMerge.append(\"feMergeNode\").attr(\"in\", \"innerGlow\");\n\n    // Create drop shadow filter\n    const shadowFilter = defs\n      .append(\"filter\")\n      .attr(\"id\", \"drop-shadow\")\n      .attr(\"x\", \"-50%\")\n      .attr(\"y\", \"-50%\")\n      .attr(\"width\", \"200%\")\n      .attr(\"height\", \"200%\");\n\n    shadowFilter\n      .append(\"feDropShadow\")\n      .attr(\"dx\", \"2\")\n      .attr(\"dy\", \"2\")\n      .attr(\"stdDeviation\", \"4\")\n      .attr(\"flood-color\", \"rgba(0,0,0,0.4)\");\n\n    // Create glass-effect gradients matching the reference image\n    bubbles.each(function (d, i) {\n      const gradient = defs\n        .append(\"radialGradient\")\n        .attr(\"id\", `gradient-${i}`)\n        .attr(\"cx\", \"30%\")\n        .attr(\"cy\", \"30%\");\n\n      // Create solid, striking bubble effect\n      const baseColor = d3.color(d.color);\n\n      // Bright center using the bubble's own color\n      gradient\n        .append(\"stop\")\n        .attr(\"offset\", \"0%\")\n        .attr(\"stop-color\", baseColor?.brighter(1.2)?.toString() || d.color)\n        .attr(\"stop-opacity\", \"0.6\");\n\n      // Transition to main color\n      gradient\n        .append(\"stop\")\n        .attr(\"offset\", \"40%\")\n        .attr(\"stop-color\", baseColor?.brighter(0.5)?.toString() || d.color)\n        .attr(\"stop-opacity\", \"0.5\");\n\n      // Main color area - more solid\n      gradient\n        .append(\"stop\")\n        .attr(\"offset\", \"70%\")\n        .attr(\"stop-color\", d.color)\n        .attr(\"stop-opacity\", \"0.4\");\n\n      // Dark outer edge for strong definition\n      gradient\n        .append(\"stop\")\n        .attr(\"offset\", \"100%\")\n        .attr(\"stop-color\", baseColor?.darker(0.5)?.toString() || d.color)\n        .attr(\"stop-opacity\", \"0.8\");\n    });\n\n    // Main transparent bubble with inner glow\n    bubbles\n      .append(\"circle\")\n      .attr(\"r\", 0)\n      .attr(\"fill\", (d, i) => `url(#gradient-${i})`)\n      .attr(\"stroke\", (d) => {\n        const baseColor = d3.color(d.color);\n        return baseColor?.brighter(0.5)?.toString() || d.color;\n      })\n      .attr(\"stroke-width\", 2.5)\n      .attr(\"stroke-opacity\", 0.7)\n      .attr(\"opacity\", 0.9)\n      .attr(\"filter\", \"url(#inner-glow)\")\n      .transition()\n      .duration(1000)\n      .delay((d, i) => i * 50)\n      .attr(\"r\", (d) => d.r)\n      .on(\"end\", function () {\n        if (this.parentNode) {\n          d3.select(this.parentNode as Element).style(\"opacity\", 1);\n        }\n      });\n\n    // Enhanced hover effects with proper transform handling\n    bubbles\n      .on(\"mouseover\", function (event, d) {\n        const circle = d3.select(this).select(\"circle\");\n\n        // Cancel any ongoing transitions to prevent conflicts\n        circle.interrupt();\n\n        // Store the current scale for this bubble\n        d.hoverScale = 1.1;\n\n        // Enhance transparency and glow on hover\n        circle\n          .transition()\n          .duration(200)\n          .attr(\"opacity\", 1)\n          .attr(\"stroke-width\", 3)\n          .attr(\"stroke\", \"#fbbf24\")\n          .attr(\"stroke-opacity\", 0.8);\n\n        // Bring to front by moving to end of parent (proper SVG z-ordering)\n        const parent = this.parentNode;\n        if (parent) {\n          parent.appendChild(this);\n        }\n\n        setHoveredBubble(d);\n        onBubbleHover?.(d);\n      })\n      .on(\"mouseout\", function (event, d) {\n        // Don't reset hover effects if this bubble is pinned\n        if (pinnedBubble?.id === d.id) return;\n\n        const circle = d3.select(this).select(\"circle\");\n\n        // Cancel any ongoing transitions to prevent conflicts\n        circle.interrupt();\n\n        // Reset the scale for this bubble\n        d.hoverScale = 1;\n\n        // Reset to transparent state\n        circle\n          .transition()\n          .duration(200)\n          .attr(\"opacity\", 0.9)\n          .attr(\"stroke-width\", 2)\n          .attr(\"stroke\", function () {\n            const baseColor = d3.color(d.color);\n            return baseColor?.brighter(0.5)?.toString() || d.color;\n          })\n          .attr(\"stroke-opacity\", 0.7);\n\n        // Only clear hover if no bubble is pinned\n        if (!pinnedBubble) {\n          setHoveredBubble(null);\n          onBubbleHover?.(null);\n        }\n      })\n      .on(\"click\", function (event, d) {\n        // Toggle pin state\n        if (pinnedBubble?.id === d.id) {\n          // Unpin if clicking the same bubble\n          setPinnedBubble(null);\n          setHoveredBubble(null);\n          onBubbleHover?.(null);\n\n          // Reset visual state\n          const circle = d3.select(this).select(\"circle\");\n          circle.interrupt();\n          d.hoverScale = 1;\n          circle\n            .transition()\n            .duration(200)\n            .attr(\"opacity\", 0.9)\n            .attr(\"stroke-width\", 2)\n            .attr(\"stroke\", function () {\n              const baseColor = d3.color(d.color);\n              return baseColor?.brighter(0.5)?.toString() || d.color;\n            })\n            .attr(\"stroke-opacity\", 0.7);\n        } else {\n          // Pin this bubble\n          setPinnedBubble(d);\n          setHoveredBubble(d);\n          onBubbleHover?.(d);\n\n          // Ensure visual state is active with pinned styling\n          const circle = d3.select(this).select(\"circle\");\n          circle.interrupt();\n          d.hoverScale = 1.1;\n          circle\n            .transition()\n            .duration(200)\n            .attr(\"opacity\", 1)\n            .attr(\"stroke-width\", 4)\n            .attr(\"stroke\", \"#fbbf24\")\n            .attr(\"stroke-opacity\", 1);\n        }\n      })\n\n    // Add symbol text - show for all bubbles with adaptive sizing\n    bubbles\n      .append(\"text\")\n      .attr(\"text-anchor\", \"middle\")\n      .attr(\"dy\", \"-0.1em\")\n      .attr(\"font-size\", (d) => Math.max(8, Math.min(d.r * 0.4, 20)))\n      .attr(\"font-weight\", \"bold\")\n      .attr(\"fill\", \"#ffffff\")\n      .attr(\"pointer-events\", \"none\")\n      .style(\"text-shadow\", \"2px 2px 4px rgba(0,0,0,0.9)\")\n      .style(\"font-family\", \"system-ui, -apple-system, sans-serif\")\n      .style(\"dominant-baseline\", \"central\")\n      .text((d) => d.symbol);\n\n    // Add percentage text - show for all bubbles\n    bubbles\n      .append(\"text\")\n      .attr(\"text-anchor\", \"middle\")\n      .attr(\"dy\", \"0.8em\")\n      .attr(\"font-size\", (d) => Math.max(6, Math.min(d.r * 0.3, 16)))\n      .attr(\"font-weight\", \"600\")\n      .attr(\"fill\", \"#ffffff\")\n      .attr(\"pointer-events\", \"none\")\n      .style(\"text-shadow\", \"2px 2px 4px rgba(0,0,0,0.9)\")\n      .style(\"font-family\", \"system-ui, -apple-system, sans-serif\")\n      .style(\"dominant-baseline\", \"central\")\n      .text((d) => `${d.change24h >= 0 ? \"+\" : \"\"}${d.change24h.toFixed(2)}%`);\n\n    // Run simulation for enough iterations to settle bubbles without overlap\n    for (let i = 0; i < 500; i++) {\n      simulation.tick();\n    }\n\n    // Update positions on simulation tick with proper scaling\n    simulation.on(\"tick\", () => {\n      bubbles.attr(\"transform\", (d) => {\n        const scale = d.clickScale || d.hoverScale || 1;\n        return `translate(${d.x},${d.y}) scale(${scale})`;\n      });\n    });\n\n    // Remove zoom functionality - bubbles should fill the screen\n\n    // Cleanup\n    return () => {\n      simulation.stop();\n    };\n  }, [data, width, height, onBubbleHover]);\n\n  return (\n    <div className=\"relative\" data-oid=\"cn3_xst\">\n      <svg\n        ref={svgRef}\n        width={width}\n        height={height}\n        className=\"bg-gradient-to-br from-gray-900 to-gray-800\"\n        style={{ display: \"block\" }}\n        data-oid=\"vyfe8jc\"\n        onClick={(e) => {\n          // Unpin bubble when clicking on background\n          if (e.target === svgRef.current) {\n            setPinnedBubble(null);\n            setHoveredBubble(null);\n            onBubbleHover?.(null);\n          }\n        }}\n      />\n\n      {/* Enhanced Tooltip */}\n      {(hoveredBubble || pinnedBubble) && (() => {\n        const displayBubble = hoveredBubble || pinnedBubble;\n        const isPinned = pinnedBubble?.id === displayBubble?.id;\n\n        return (\n          <div\n            className={`absolute top-4 right-4 bg-black/20 p-5 rounded-xl shadow-2xl border-2 z-20 min-w-72 max-w-80 backdrop-blur-md ${\n              isPinned ? 'border-yellow-400/50' : 'border-white/30'\n            }`}\n            style={{\n              boxShadow:\n                \"inset 0 0 20px rgba(255, 255, 255, 0.1), 0 8px 32px rgba(0, 0, 0, 0.3)\",\n            }}\n            data-oid=\"km3npxp\"\n          >\n            {isPinned && (\n              <div className=\"absolute -top-2 -right-2 w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center\">\n                <span className=\"text-black text-xs font-bold\">📌</span>\n              </div>\n            )}\n            <div className=\"flex items-center gap-3 mb-3\" data-oid=\"jc0k_nz\">\n              <div\n                className=\"w-10 h-10 rounded-full flex items-center justify-center text-white font-bold text-sm\"\n                style={{\n                  backgroundColor:\n                    displayBubble.change24h >= 0 ? \"#22c55e\" : \"#ef4444\",\n                }}\n                data-oid=\"dejxve:\"\n              >\n                {displayBubble.symbol.charAt(0)}\n              </div>\n              <div data-oid=\"1ok57-r\">\n                <h3 className=\"font-bold text-lg text-white\" data-oid=\"wmdqdov\">\n                  {displayBubble.name}\n                </h3>\n                <span\n                  className=\"text-white/80 text-sm font-medium\"\n                  data-oid=\"evx3o28\"\n                >\n                  ({displayBubble.symbol}) • Rank #{displayBubble.rank}\n                </span>\n              </div>\n            </div>\n\n            <div className=\"space-y-2 text-sm\" data-oid=\"h0nfuhh\">\n              <div\n                className=\"flex justify-between items-center\"\n                data-oid=\"d6yjrk9\"\n              >\n                <span className=\"text-white/90 font-medium\" data-oid=\"6m.58cj\">\n                  Price:\n                </span>\n                <span className=\"font-bold text-lg text-white\" data-oid=\"0bj4iet\">\n                  $\n                  {displayBubble.price < 1\n                    ? displayBubble.price.toFixed(4)\n                    : displayBubble.price.toLocaleString()}\n                </span>\n              </div>\n\n              <div\n                className=\"flex justify-between items-center\"\n                data-oid=\"07x2:ab\"\n              >\n                <span className=\"text-white/90 font-medium\" data-oid=\"ot1y:3s\">\n                  Market Cap:\n                </span>\n                <span className=\"font-semibold text-white\" data-oid=\"t5x.htn\">\n                  {displayBubble.marketCap >= 1e9\n                    ? `$${(displayBubble.marketCap / 1e9).toFixed(2)}B`\n                    : `$${(displayBubble.marketCap / 1e6).toFixed(2)}M`}\n                </span>\n              </div>\n\n              <div\n                className=\"flex justify-between items-center\"\n                data-oid=\"hpxbbcg\"\n              >\n                <span className=\"text-white/90 font-medium\" data-oid=\"4myuzet\">\n                  24h Volume:\n                </span>\n                <span className=\"font-semibold text-white\" data-oid=\"pbh7rf.\">\n                  {displayBubble.volume24h >= 1e9\n                    ? `$${(displayBubble.volume24h / 1e9).toFixed(2)}B`\n                    : `$${(displayBubble.volume24h / 1e6).toFixed(2)}M`}\n                </span>\n              </div>\n\n              <div\n                className=\"flex justify-between items-center\"\n                data-oid=\"rli2gdt\"\n              >\n                <span className=\"text-white/90 font-medium\" data-oid=\"_87x9tp\">\n                  24h Change:\n                </span>\n                <div\n                  className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs font-bold ${\n                    displayBubble.change24h >= 0\n                      ? \"bg-green-500/20 text-green-300 border border-green-400/30\"\n                      : \"bg-red-500/20 text-red-300 border border-red-400/30\"\n                  }`}\n                  data-oid=\"wxq.s6:\"\n                >\n                  <span data-oid=\"rdhy:ha\">\n                    {displayBubble.change24h >= 0 ? \"↗\" : \"↘\"}\n                  </span>\n                  <span data-oid=\"f-r8.4q\">\n                    {displayBubble.change24h >= 0 ? \"+\" : \"\"}\n                    {displayBubble.change24h.toFixed(2)}%\n                  </span>\n                </div>\n              </div>\n\n              <div\n                className=\"flex justify-between items-center\"\n                data-oid=\"a4w:gre\"\n              >\n                <span className=\"text-white/90 font-medium\" data-oid=\"n46j1tl\">\n                  7d Change:\n                </span>\n                <span\n                  className={`font-semibold ${displayBubble.change7d >= 0 ? \"text-green-300\" : \"text-red-300\"}`}\n                  data-oid=\"8cu:u_m\"\n                >\n                  {displayBubble.change7d >= 0 ? \"+\" : \"\"}\n                  {displayBubble.change7d.toFixed(2)}%\n                </span>\n              </div>\n\n              <div className=\"pt-2 border-t border-white/30\" data-oid=\"_ul03a5\">\n                <div\n                  className=\"flex justify-between items-center\"\n                  data-oid=\"yrv.:ln\"\n                >\n                  <span className=\"text-white/90 font-medium\" data-oid=\"mwc:cg_\">\n                    Category:\n                  </span>\n                  <span\n                    className=\"px-2 py-1 bg-blue-500/20 text-blue-300 border border-blue-400/30 rounded-full text-xs font-bold\"\n                    data-oid=\"y9j.jp4\"\n                  >\n                    {displayBubble.category}\n                  </span>\n                </div>\n              </div>\n            </div>\n\n            <div\n              className=\"mt-3 pt-3 border-t border-white/30 text-xs text-white/80 text-center font-medium\"\n              data-oid=\"ofbq3_c\"\n            >\n              {isPinned ? 'Click to unpin • Drag to move' : 'Click to pin • Drag to move'}\n            </div>\n          </div>\n        );\n      })()}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAaO,MAAM,cAA0C,CAAC,EACtD,IAAI,EACJ,QAAQ,IAAI,EACZ,SAAS,GAAG,EACZ,aAAa,EACd;IACC,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAiB;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAC/C;IAEF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAC7C;IAGF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,OAAO,OAAO,IAAI,CAAC,KAAK,MAAM,EAAE;QAErC,MAAM,MAAM,CAAA,GAAA,qLAAA,CAAA,SAAS,AAAD,EAAE,OAAO,OAAO;QACpC,IAAI,SAAS,CAAC,KAAK,MAAM;QAEzB,gBAAgB;QAChB,MAAM,eAAe,CAAA,GAAA,2KAAA,CAAA,MAAM,AAAD,EAAE,MAAM,CAAC,IAAM,EAAE,SAAS,KAAK;QACzD,MAAM,eAAe,CAAA,GAAA,2KAAA,CAAA,MAAM,AAAD,EAAE,MAAM,CAAC,IAAM,EAAE,SAAS,KAAK;QAEzD,oEAAoE;QACpE,MAAM,YAAY,QAAQ;QAC1B,MAAM,cAAc,KAAK,MAAM;QAC/B,MAAM,cAAc,AAAC,YAAY,cAAe,KAAK,iDAAiD;QACtG,MAAM,gBAAgB,KAAK,IAAI,CAAC,cAAc,KAAK,EAAE;QAErD,wDAAwD;QACxD,MAAM,cAAc,CAAA,GAAA,8KAAA,CAAA,YACR,AAAD,IACR,MAAM,CAAC;YAAC;YAAc;SAAa,EACnC,KAAK,CAAC;YAAC,gBAAgB;YAAK,gBAAgB;SAAI,GAAG,2BAA2B;QAEjF,yDAAyD;QACzD,MAAM,aAAa,CAAA,GAAA,sLAAA,CAAA,cACL,AAAD,IACV,MAAM,CAAC;YAAC,CAAC;YAAI,CAAC;YAAG;YAAG;YAAG;SAAG,EAC1B,KAAK,CAAC;YAAC;YAAW;YAAW;YAAW;YAAW;SAAU,EAC7D,KAAK,CAAC;QAET,sBAAsB;QACtB,MAAM,aAA2B,KAAK,GAAG,CAAC,CAAC,SAAW,CAAC;gBACrD,GAAG,MAAM;gBACT,GAAG;gBACH,GAAG;gBACH,GAAG,YAAY,OAAO,SAAS;gBAC/B,OAAO,WAAW,OAAO,SAAS;YACpC,CAAC;QAED,yEAAyE;QACzE,MAAM,aAAa,CAAA,GAAA,8LAAA,CAAA,kBACD,AAAD,EAAc,YAC5B,KAAK,CAAC,UAAU,CAAA,GAAA,0LAAA,CAAA,gBAAgB,AAAD,IAAI,QAAQ,CAAC,CAAC,KAAK,wCAAwC;SAC1F,KAAK,CAAC,UAAU,CAAA,GAAA,sLAAA,CAAA,cAAc,AAAD,EAAE,QAAQ,GAAG,SAAS,IACnD,KAAK,CACJ,aACA,CAAA,GAAA,wLAAA,CAAA,eACe,AAAD,IACX,MAAM,CAAC,CAAC,IAAM,EAAE,CAAC,GAAG,GAAG,mCAAmC;SAC1D,QAAQ,CAAC,GAAG,6BAA6B;SACzC,UAAU,CAAC,IACd,sDAAsD;SACvD,KAAK,CAAC,KAAK,CAAA,GAAA,4KAAA,CAAA,SAAS,AAAD,EAAc,QAAQ,GAAG,QAAQ,CAAC,OAAO,mBAAmB;SAC/E,KAAK,CAAC,KAAK,CAAA,GAAA,4KAAA,CAAA,SAAS,AAAD,EAAc,SAAS,GAAG,QAAQ,CAAC,MACvD,gDAAgD;SAC/C,KAAK,CAAC,YAAY;YACjB,WAAW,OAAO,CAAC,CAAC;gBAClB,MAAM,UAAU,EAAE,CAAC,GAAG;gBACtB,EAAE,CAAC,GAAG,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC,QAAQ,SAAS,EAAE,CAAC,IAAI,QAAQ;gBACjE,EAAE,CAAC,GAAG,KAAK,GAAG,CACZ,SACA,KAAK,GAAG,CAAC,SAAS,SAAS,EAAE,CAAC,IAAI,SAAS;YAE/C;QACF,GACC,UAAU,CAAC,OAAO,wCAAwC;SAC1D,aAAa,CAAC,MAAM,gCAAgC;QAEvD,yBAAyB;QACzB,MAAM,YAAY,IAAI,MAAM,CAAC;QAE7B,yCAAyC;QACzC,MAAM,UAAU,UACb,SAAS,CAAC,WACV,IAAI,CAAC,YACL,KAAK,GACL,MAAM,CAAC,KACP,IAAI,CAAC,SAAS,UACd,KAAK,CAAC,UAAU,WAChB,KAAK,CAAC,WAAW;QAEpB,mDAAmD;QACnD,MAAM,OAAO,IAAI,MAAM,CAAC;QAExB,6DAA6D;QAC7D,MAAM,kBAAkB,KACrB,MAAM,CAAC,UACP,IAAI,CAAC,MAAM,cACX,IAAI,CAAC,KAAK,QACV,IAAI,CAAC,KAAK,QACV,IAAI,CAAC,SAAS,QACd,IAAI,CAAC,UAAU;QAElB,qCAAqC;QACrC,gBACG,MAAM,CAAC,eACP,IAAI,CAAC,MAAM,eACX,IAAI,CAAC,OAAO,eACZ,IAAI,CAAC,YAAY,OACjB,IAAI,CAAC,UAAU;QAElB,iDAAiD;QACjD,gBACG,MAAM,CAAC,kBACP,IAAI,CAAC,MAAM,WACX,IAAI,CAAC,gBAAgB,KACrB,IAAI,CAAC,UAAU;QAElB,6DAA6D;QAC7D,gBACG,MAAM,CAAC,eACP,IAAI,CAAC,MAAM,WACX,IAAI,CAAC,OAAO,eACZ,IAAI,CAAC,YAAY,MACjB,IAAI,CAAC,UAAU;QAElB,oDAAoD;QACpD,MAAM,UAAU,gBAAgB,MAAM,CAAC;QACvC,QAAQ,MAAM,CAAC,eAAe,IAAI,CAAC,MAAM;QACzC,QAAQ,MAAM,CAAC,eAAe,IAAI,CAAC,MAAM;QAEzC,4BAA4B;QAC5B,MAAM,eAAe,KAClB,MAAM,CAAC,UACP,IAAI,CAAC,MAAM,eACX,IAAI,CAAC,KAAK,QACV,IAAI,CAAC,KAAK,QACV,IAAI,CAAC,SAAS,QACd,IAAI,CAAC,UAAU;QAElB,aACG,MAAM,CAAC,gBACP,IAAI,CAAC,MAAM,KACX,IAAI,CAAC,MAAM,KACX,IAAI,CAAC,gBAAgB,KACrB,IAAI,CAAC,eAAe;QAEvB,6DAA6D;QAC7D,QAAQ,IAAI,CAAC,SAAU,CAAC,EAAE,CAAC;YACzB,MAAM,WAAW,KACd,MAAM,CAAC,kBACP,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,GAAG,EAC1B,IAAI,CAAC,MAAM,OACX,IAAI,CAAC,MAAM;YAEd,uCAAuC;YACvC,MAAM,YAAY,CAAA,GAAA,+KAAA,CAAA,QAAQ,AAAD,EAAE,EAAE,KAAK;YAElC,6CAA6C;YAC7C,SACG,MAAM,CAAC,QACP,IAAI,CAAC,UAAU,MACf,IAAI,CAAC,cAAc,WAAW,SAAS,MAAM,cAAc,EAAE,KAAK,EAClE,IAAI,CAAC,gBAAgB;YAExB,2BAA2B;YAC3B,SACG,MAAM,CAAC,QACP,IAAI,CAAC,UAAU,OACf,IAAI,CAAC,cAAc,WAAW,SAAS,MAAM,cAAc,EAAE,KAAK,EAClE,IAAI,CAAC,gBAAgB;YAExB,+BAA+B;YAC/B,SACG,MAAM,CAAC,QACP,IAAI,CAAC,UAAU,OACf,IAAI,CAAC,cAAc,EAAE,KAAK,EAC1B,IAAI,CAAC,gBAAgB;YAExB,wCAAwC;YACxC,SACG,MAAM,CAAC,QACP,IAAI,CAAC,UAAU,QACf,IAAI,CAAC,cAAc,WAAW,OAAO,MAAM,cAAc,EAAE,KAAK,EAChE,IAAI,CAAC,gBAAgB;QAC1B;QAEA,0CAA0C;QAC1C,QACG,MAAM,CAAC,UACP,IAAI,CAAC,KAAK,GACV,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAM,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC,EAC5C,IAAI,CAAC,UAAU,CAAC;YACf,MAAM,YAAY,CAAA,GAAA,+KAAA,CAAA,QAAQ,AAAD,EAAE,EAAE,KAAK;YAClC,OAAO,WAAW,SAAS,MAAM,cAAc,EAAE,KAAK;QACxD,GACC,IAAI,CAAC,gBAAgB,KACrB,IAAI,CAAC,kBAAkB,KACvB,IAAI,CAAC,WAAW,KAChB,IAAI,CAAC,UAAU,oBACf,UAAU,GACV,QAAQ,CAAC,MACT,KAAK,CAAC,CAAC,GAAG,IAAM,IAAI,IACpB,IAAI,CAAC,KAAK,CAAC,IAAM,EAAE,CAAC,EACpB,EAAE,CAAC,OAAO;YACT,IAAI,IAAI,CAAC,UAAU,EAAE;gBACnB,CAAA,GAAA,qLAAA,CAAA,SAAS,AAAD,EAAE,IAAI,CAAC,UAAU,EAAa,KAAK,CAAC,WAAW;YACzD;QACF;QAEF,wDAAwD;QACxD,QACG,EAAE,CAAC,aAAa,SAAU,KAAK,EAAE,CAAC;YACjC,MAAM,SAAS,CAAA,GAAA,qLAAA,CAAA,SAAS,AAAD,EAAE,IAAI,EAAE,MAAM,CAAC;YAEtC,sDAAsD;YACtD,OAAO,SAAS;YAEhB,0CAA0C;YAC1C,EAAE,UAAU,GAAG;YAEf,yCAAyC;YACzC,OACG,UAAU,GACV,QAAQ,CAAC,KACT,IAAI,CAAC,WAAW,GAChB,IAAI,CAAC,gBAAgB,GACrB,IAAI,CAAC,UAAU,WACf,IAAI,CAAC,kBAAkB;YAE1B,oEAAoE;YACpE,MAAM,SAAS,IAAI,CAAC,UAAU;YAC9B,IAAI,QAAQ;gBACV,OAAO,WAAW,CAAC,IAAI;YACzB;YAEA,iBAAiB;YACjB,gBAAgB;QAClB,GACC,EAAE,CAAC,YAAY,SAAU,KAAK,EAAE,CAAC;YAChC,qDAAqD;YACrD,IAAI,cAAc,OAAO,EAAE,EAAE,EAAE;YAE/B,MAAM,SAAS,CAAA,GAAA,qLAAA,CAAA,SAAS,AAAD,EAAE,IAAI,EAAE,MAAM,CAAC;YAEtC,sDAAsD;YACtD,OAAO,SAAS;YAEhB,kCAAkC;YAClC,EAAE,UAAU,GAAG;YAEf,6BAA6B;YAC7B,OACG,UAAU,GACV,QAAQ,CAAC,KACT,IAAI,CAAC,WAAW,KAChB,IAAI,CAAC,gBAAgB,GACrB,IAAI,CAAC,UAAU;gBACd,MAAM,YAAY,CAAA,GAAA,+KAAA,CAAA,QAAQ,AAAD,EAAE,EAAE,KAAK;gBAClC,OAAO,WAAW,SAAS,MAAM,cAAc,EAAE,KAAK;YACxD,GACC,IAAI,CAAC,kBAAkB;YAE1B,0CAA0C;YAC1C,IAAI,CAAC,cAAc;gBACjB,iBAAiB;gBACjB,gBAAgB;YAClB;QACF,GACC,EAAE,CAAC,SAAS,SAAU,KAAK,EAAE,CAAC;YAC7B,mBAAmB;YACnB,IAAI,cAAc,OAAO,EAAE,EAAE,EAAE;gBAC7B,oCAAoC;gBACpC,gBAAgB;gBAChB,iBAAiB;gBACjB,gBAAgB;gBAEhB,qBAAqB;gBACrB,MAAM,SAAS,CAAA,GAAA,qLAAA,CAAA,SAAS,AAAD,EAAE,IAAI,EAAE,MAAM,CAAC;gBACtC,OAAO,SAAS;gBAChB,EAAE,UAAU,GAAG;gBACf,OACG,UAAU,GACV,QAAQ,CAAC,KACT,IAAI,CAAC,WAAW,KAChB,IAAI,CAAC,gBAAgB,GACrB,IAAI,CAAC,UAAU;oBACd,MAAM,YAAY,CAAA,GAAA,+KAAA,CAAA,QAAQ,AAAD,EAAE,EAAE,KAAK;oBAClC,OAAO,WAAW,SAAS,MAAM,cAAc,EAAE,KAAK;gBACxD,GACC,IAAI,CAAC,kBAAkB;YAC5B,OAAO;gBACL,kBAAkB;gBAClB,gBAAgB;gBAChB,iBAAiB;gBACjB,gBAAgB;gBAEhB,oDAAoD;gBACpD,MAAM,SAAS,CAAA,GAAA,qLAAA,CAAA,SAAS,AAAD,EAAE,IAAI,EAAE,MAAM,CAAC;gBACtC,OAAO,SAAS;gBAChB,EAAE,UAAU,GAAG;gBACf,OACG,UAAU,GACV,QAAQ,CAAC,KACT,IAAI,CAAC,WAAW,GAChB,IAAI,CAAC,gBAAgB,GACrB,IAAI,CAAC,UAAU,WACf,IAAI,CAAC,kBAAkB;YAC5B;QACF;QAEF,8DAA8D;QAC9D,QACG,MAAM,CAAC,QACP,IAAI,CAAC,eAAe,UACpB,IAAI,CAAC,MAAM,UACX,IAAI,CAAC,aAAa,CAAC,IAAM,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,MACzD,IAAI,CAAC,eAAe,QACpB,IAAI,CAAC,QAAQ,WACb,IAAI,CAAC,kBAAkB,QACvB,KAAK,CAAC,eAAe,+BACrB,KAAK,CAAC,eAAe,wCACrB,KAAK,CAAC,qBAAqB,WAC3B,IAAI,CAAC,CAAC,IAAM,EAAE,MAAM;QAEvB,6CAA6C;QAC7C,QACG,MAAM,CAAC,QACP,IAAI,CAAC,eAAe,UACpB,IAAI,CAAC,MAAM,SACX,IAAI,CAAC,aAAa,CAAC,IAAM,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,MACzD,IAAI,CAAC,eAAe,OACpB,IAAI,CAAC,QAAQ,WACb,IAAI,CAAC,kBAAkB,QACvB,KAAK,CAAC,eAAe,+BACrB,KAAK,CAAC,eAAe,wCACrB,KAAK,CAAC,qBAAqB,WAC3B,IAAI,CAAC,CAAC,IAAM,GAAG,EAAE,SAAS,IAAI,IAAI,MAAM,KAAK,EAAE,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAEzE,yEAAyE;QACzE,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC5B,WAAW,IAAI;QACjB;QAEA,0DAA0D;QAC1D,WAAW,EAAE,CAAC,QAAQ;YACpB,QAAQ,IAAI,CAAC,aAAa,CAAC;gBACzB,MAAM,QAAQ,EAAE,UAAU,IAAI,EAAE,UAAU,IAAI;gBAC9C,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YACnD;QACF;QAEA,6DAA6D;QAE7D,UAAU;QACV,OAAO;YACL,WAAW,IAAI;QACjB;IACF,GAAG;QAAC;QAAM;QAAO;QAAQ;KAAc;IAEvC,qBACE,8OAAC;QAAI,WAAU;QAAW,YAAS;;0BACjC,8OAAC;gBACC,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,WAAU;gBACV,OAAO;oBAAE,SAAS;gBAAQ;gBAC1B,YAAS;gBACT,SAAS,CAAC;oBACR,2CAA2C;oBAC3C,IAAI,EAAE,MAAM,KAAK,OAAO,OAAO,EAAE;wBAC/B,gBAAgB;wBAChB,iBAAiB;wBACjB,gBAAgB;oBAClB;gBACF;;;;;;YAID,CAAC,iBAAiB,YAAY,KAAK,CAAC;gBACnC,MAAM,gBAAgB,iBAAiB;gBACvC,MAAM,WAAW,cAAc,OAAO,eAAe;gBAErD,qBACE,8OAAC;oBACC,WAAW,CAAC,8GAA8G,EACxH,WAAW,yBAAyB,mBACpC;oBACF,OAAO;wBACL,WACE;oBACJ;oBACA,YAAS;;wBAER,0BACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CAA+B;;;;;;;;;;;sCAGnD,8OAAC;4BAAI,WAAU;4BAA+B,YAAS;;8CACrD,8OAAC;oCACC,WAAU;oCACV,OAAO;wCACL,iBACE,cAAc,SAAS,IAAI,IAAI,YAAY;oCAC/C;oCACA,YAAS;8CAER,cAAc,MAAM,CAAC,MAAM,CAAC;;;;;;8CAE/B,8OAAC;oCAAI,YAAS;;sDACZ,8OAAC;4CAAG,WAAU;4CAA+B,YAAS;sDACnD,cAAc,IAAI;;;;;;sDAErB,8OAAC;4CACC,WAAU;4CACV,YAAS;;gDACV;gDACG,cAAc,MAAM;gDAAC;gDAAW,cAAc,IAAI;;;;;;;;;;;;;;;;;;;sCAK1D,8OAAC;4BAAI,WAAU;4BAAoB,YAAS;;8CAC1C,8OAAC;oCACC,WAAU;oCACV,YAAS;;sDAET,8OAAC;4CAAK,WAAU;4CAA4B,YAAS;sDAAU;;;;;;sDAG/D,8OAAC;4CAAK,WAAU;4CAA+B,YAAS;;gDAAU;gDAE/D,cAAc,KAAK,GAAG,IACnB,cAAc,KAAK,CAAC,OAAO,CAAC,KAC5B,cAAc,KAAK,CAAC,cAAc;;;;;;;;;;;;;8CAI1C,8OAAC;oCACC,WAAU;oCACV,YAAS;;sDAET,8OAAC;4CAAK,WAAU;4CAA4B,YAAS;sDAAU;;;;;;sDAG/D,8OAAC;4CAAK,WAAU;4CAA2B,YAAS;sDACjD,cAAc,SAAS,IAAI,MACxB,CAAC,CAAC,EAAE,CAAC,cAAc,SAAS,GAAG,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,GACjD,CAAC,CAAC,EAAE,CAAC,cAAc,SAAS,GAAG,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;;;;;;;;;;;;8CAIzD,8OAAC;oCACC,WAAU;oCACV,YAAS;;sDAET,8OAAC;4CAAK,WAAU;4CAA4B,YAAS;sDAAU;;;;;;sDAG/D,8OAAC;4CAAK,WAAU;4CAA2B,YAAS;sDACjD,cAAc,SAAS,IAAI,MACxB,CAAC,CAAC,EAAE,CAAC,cAAc,SAAS,GAAG,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,GACjD,CAAC,CAAC,EAAE,CAAC,cAAc,SAAS,GAAG,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;;;;;;;;;;;;8CAIzD,8OAAC;oCACC,WAAU;oCACV,YAAS;;sDAET,8OAAC;4CAAK,WAAU;4CAA4B,YAAS;sDAAU;;;;;;sDAG/D,8OAAC;4CACC,WAAW,CAAC,iEAAiE,EAC3E,cAAc,SAAS,IAAI,IACvB,8DACA,uDACJ;4CACF,YAAS;;8DAET,8OAAC;oDAAK,YAAS;8DACZ,cAAc,SAAS,IAAI,IAAI,MAAM;;;;;;8DAExC,8OAAC;oDAAK,YAAS;;wDACZ,cAAc,SAAS,IAAI,IAAI,MAAM;wDACrC,cAAc,SAAS,CAAC,OAAO,CAAC;wDAAG;;;;;;;;;;;;;;;;;;;8CAK1C,8OAAC;oCACC,WAAU;oCACV,YAAS;;sDAET,8OAAC;4CAAK,WAAU;4CAA4B,YAAS;sDAAU;;;;;;sDAG/D,8OAAC;4CACC,WAAW,CAAC,cAAc,EAAE,cAAc,QAAQ,IAAI,IAAI,mBAAmB,gBAAgB;4CAC7F,YAAS;;gDAER,cAAc,QAAQ,IAAI,IAAI,MAAM;gDACpC,cAAc,QAAQ,CAAC,OAAO,CAAC;gDAAG;;;;;;;;;;;;;8CAIvC,8OAAC;oCAAI,WAAU;oCAAgC,YAAS;8CACtD,cAAA,8OAAC;wCACC,WAAU;wCACV,YAAS;;0DAET,8OAAC;gDAAK,WAAU;gDAA4B,YAAS;0DAAU;;;;;;0DAG/D,8OAAC;gDACC,WAAU;gDACV,YAAS;0DAER,cAAc,QAAQ;;;;;;;;;;;;;;;;;;;;;;;sCAM/B,8OAAC;4BACC,WAAU;4BACV,YAAS;sCAER,WAAW,kCAAkC;;;;;;;;;;;;YAItD,CAAC;;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 577, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/dev/bubblekrc/cryptobubble-clone/src/components/LoadingSpinner.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\n\ninterface LoadingSpinnerProps {\n  size?: \"sm\" | \"md\" | \"lg\";\n  text?: string;\n}\n\nexport const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({\n  size = \"md\",\n  text = \"Loading...\",\n}) => {\n  const sizeClasses = {\n    sm: \"w-4 h-4\",\n    md: \"w-8 h-8\",\n    lg: \"w-12 h-12\",\n  };\n\n  return (\n    <div\n      className=\"flex flex-col items-center justify-center p-8\"\n      data-oid=\"6gmp8xi\"\n    >\n      <div\n        className={`${sizeClasses[size]} animate-spin rounded-full border-4 border-gray-300 border-t-blue-600`}\n        data-oid=\"o81jg6y\"\n      ></div>\n      {text && (\n        <p className=\"mt-4 text-gray-600 text-sm\" data-oid=\"ovy93vh\">\n          {text}\n        </p>\n      )}\n    </div>\n  );\n};\n\nexport const BubbleChartSkeleton: React.FC = () => {\n  return (\n    <div\n      className=\"w-full h-[700px] bg-gradient-to-br from-gray-900 to-gray-800 rounded-lg border border-gray-200 flex items-center justify-center\"\n      data-oid=\"n18j76x\"\n    >\n      <div className=\"text-center\" data-oid=\"q_ecqx.\">\n        <div\n          className=\"w-16 h-16 animate-spin rounded-full border-4 border-gray-600 border-t-blue-400 mx-auto mb-4\"\n          data-oid=\"4jdehoc\"\n        ></div>\n        <p className=\"text-white text-lg font-medium\" data-oid=\":f8jvvv\">\n          Loading cryptocurrency data...\n        </p>\n        <p className=\"text-gray-400 text-sm mt-2\" data-oid=\"h0lsp4m\">\n          Preparing interactive bubble chart\n        </p>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAAA;;AASO,MAAM,iBAAgD,CAAC,EAC5D,OAAO,IAAI,EACX,OAAO,YAAY,EACpB;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAU;QACV,YAAS;;0BAET,8OAAC;gBACC,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,qEAAqE,CAAC;gBACtG,YAAS;;;;;;YAEV,sBACC,8OAAC;gBAAE,WAAU;gBAA6B,YAAS;0BAChD;;;;;;;;;;;;AAKX;AAEO,MAAM,sBAAgC;IAC3C,qBACE,8OAAC;QACC,WAAU;QACV,YAAS;kBAET,cAAA,8OAAC;YAAI,WAAU;YAAc,YAAS;;8BACpC,8OAAC;oBACC,WAAU;oBACV,YAAS;;;;;;8BAEX,8OAAC;oBAAE,WAAU;oBAAiC,YAAS;8BAAU;;;;;;8BAGjE,8OAAC;oBAAE,WAAU;oBAA6B,YAAS;8BAAU;;;;;;;;;;;;;;;;;AAMrE", "debugId": null}}, {"offset": {"line": 670, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/dev/bubblekrc/cryptobubble-clone/src/components/ResponsiveBubbleChart.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect, useRef } from \"react\";\nimport { Bubble<PERSON><PERSON> } from \"./BubbleChart\";\nimport { BubbleChartSkeleton } from \"./LoadingSpinner\";\nimport { CryptoCurrency } from \"@/types\";\n\ninterface ResponsiveBubbleChartProps {\n  data: CryptoCurrency[];\n  onBubbleHover?: (crypto: CryptoCurrency | null) => void;\n  isLoading?: boolean;\n}\n\nexport const ResponsiveBubbleChart: React.FC<ResponsiveBubbleChartProps> = ({\n  data,\n  onBubbleHover,\n  isLoading = false,\n}) => {\n  const containerRef = useRef<HTMLDivElement>(null);\n  const [dimensions, setDimensions] = useState({\n    width: typeof window !== \"undefined\" ? window.innerWidth : 1920,\n    height: typeof window !== \"undefined\" ? window.innerHeight : 1080,\n  });\n\n  useEffect(() => {\n    const updateDimensions = () => {\n      // Use full viewport dimensions\n      setDimensions({\n        width: window.innerWidth,\n        height: window.innerHeight,\n      });\n    };\n\n    updateDimensions();\n    window.addEventListener(\"resize\", updateDimensions);\n\n    return () => window.removeEventListener(\"resize\", updateDimensions);\n  }, []);\n\n  if (isLoading) {\n    return <BubbleChartSkeleton />;\n  }\n\n  return (\n    <div ref={containerRef} className=\"fixed inset-0 w-full h-full\">\n      <BubbleChart\n        data={data}\n        width={dimensions.width}\n        height={dimensions.height}\n        onBubbleHover={onBubbleHover}\n      />\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAaO,MAAM,wBAA8D,CAAC,EAC1E,IAAI,EACJ,aAAa,EACb,YAAY,KAAK,EAClB;IACC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,OAAO,6EAAoD;QAC3D,QAAQ,6EAAqD;IAC/D;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,+BAA+B;YAC/B,cAAc;gBACZ,OAAO,OAAO,UAAU;gBACxB,QAAQ,OAAO,WAAW;YAC5B;QACF;QAEA;QACA,OAAO,gBAAgB,CAAC,UAAU;QAElC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,IAAI,WAAW;QACb,qBAAO,8OAAC,oIAAA,CAAA,sBAAmB;;;;;IAC7B;IAEA,qBACE,8OAAC;QAAI,KAAK;QAAc,WAAU;kBAChC,cAAA,8OAAC,iIAAA,CAAA,cAAW;YACV,MAAM;YACN,OAAO,WAAW,KAAK;YACvB,QAAQ,WAAW,MAAM;YACzB,eAAe;;;;;;;;;;;AAIvB", "debugId": null}}, {"offset": {"line": 732, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/dev/bubblekrc/cryptobubble-clone/src/components/SearchAndFilter.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { Search, Filter, TrendingUp, TrendingDown } from \"lucide-react\";\nimport { FilterOptions } from \"@/types\";\n\ninterface SearchAndFilterProps {\n  filters: FilterOptions;\n  onFiltersChange: (filters: FilterOptions) => void;\n  categories: string[];\n  compact?: boolean;\n}\n\nexport const SearchAndFilter: React.FC<SearchAndFilterProps> = ({\n  filters,\n  onFiltersChange,\n  categories,\n  compact = false,\n}) => {\n  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    onFiltersChange({ ...filters, searchTerm: e.target.value });\n  };\n\n  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n    onFiltersChange({\n      ...filters,\n      category: e.target.value === \"all\" ? undefined : e.target.value,\n    });\n  };\n\n  const handleSortChange = (sortBy: FilterOptions[\"sortBy\"]) => {\n    const newSortOrder =\n      filters.sortBy === sortBy && filters.sortOrder === \"desc\"\n        ? \"asc\"\n        : \"desc\";\n    onFiltersChange({ ...filters, sortBy, sortOrder: newSortOrder });\n  };\n\n  if (compact) {\n    return (\n      <div className=\"space-y-3\">\n        {/* Compact Search */}\n        <div className=\"relative\">\n          <Search className=\"absolute left-2 top-1/2 transform -translate-y-1/2 text-white/70 w-3 h-3\" />\n\n          <input\n            type=\"text\"\n            placeholder=\"Search...\"\n            value={filters.searchTerm || \"\"}\n            onChange={handleSearchChange}\n            className=\"w-full pl-7 pr-3 py-1.5 text-sm text-white font-medium bg-white/10 border border-white/30 rounded focus:ring-1 focus:ring-blue-400 focus:border-blue-400 placeholder-white/60 backdrop-blur-sm\"\n          />\n        </div>\n\n        {/* Compact Category Filter */}\n        <select\n          value={filters.category || \"all\"}\n          onChange={handleCategoryChange}\n          className=\"w-full px-2 py-1.5 text-sm text-white font-medium bg-white/10 border border-white/30 rounded focus:ring-1 focus:ring-blue-400 focus:border-blue-400 backdrop-blur-sm\"\n        >\n          <option value=\"all\">All Categories</option>\n          {categories.map((category) => (\n            <option key={category} value={category}>\n              {category}\n            </option>\n          ))}\n        </select>\n\n        {/* Compact Sort Options */}\n        <div className=\"flex gap-1\">\n          <button\n            onClick={() => handleSortChange(\"marketCap\")}\n            className={`px-2 py-1 text-xs font-semibold rounded border transition-colors ${\n              filters.sortBy === \"marketCap\"\n                ? \"bg-blue-500 text-white border-blue-500\"\n                : \"bg-white/10 text-white border-white/30 hover:bg-white/20 backdrop-blur-sm\"\n            }`}\n          >\n            Cap\n          </button>\n\n          <button\n            onClick={() => handleSortChange(\"change24h\")}\n            className={`px-2 py-1 text-xs font-semibold rounded border transition-colors ${\n              filters.sortBy === \"change24h\"\n                ? \"bg-blue-500 text-white border-blue-500\"\n                : \"bg-white/10 text-white border-white/30 hover:bg-white/20 backdrop-blur-sm\"\n            }`}\n          >\n            24h\n          </button>\n\n          <button\n            onClick={() => handleSortChange(\"price\")}\n            className={`px-2 py-1 text-xs font-semibold rounded border transition-colors ${\n              filters.sortBy === \"price\"\n                ? \"bg-blue-500 text-white border-blue-500\"\n                : \"bg-white/10 text-white border-white/30 hover:bg-white/20 backdrop-blur-sm\"\n            }`}\n          >\n            Price\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white p-6 rounded-lg shadow-lg border mb-6\">\n      <div className=\"flex flex-col lg:flex-row gap-4 items-center\">\n        {/* Search */}\n        <div className=\"relative flex-1 min-w-64\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-600 w-4 h-4\" />\n\n          <input\n            type=\"text\"\n            placeholder=\"Search cryptocurrencies...\"\n            value={filters.searchTerm || \"\"}\n            onChange={handleSearchChange}\n            className=\"w-full pl-10 pr-4 py-2 text-black font-medium border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent placeholder-gray-600\"\n          />\n        </div>\n\n        {/* Category Filter */}\n        <div className=\"flex items-center gap-2\">\n          <Filter className=\"text-gray-600 w-4 h-4\" />\n          <select\n            value={filters.category || \"all\"}\n            onChange={handleCategoryChange}\n            className=\"px-3 py-2 text-black font-medium border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          >\n            <option value=\"all\">All Categories</option>\n            {categories.map((category) => (\n              <option key={category} value={category}>\n                {category}\n              </option>\n            ))}\n          </select>\n        </div>\n\n        {/* Sort Options */}\n        <div className=\"flex gap-2\">\n          <button\n            onClick={() => handleSortChange(\"marketCap\")}\n            className={`px-3 py-2 font-semibold rounded-lg border transition-colors ${\n              filters.sortBy === \"marketCap\"\n                ? \"bg-blue-500 text-white border-blue-500\"\n                : \"bg-white text-black border-gray-300 hover:bg-gray-50\"\n            }`}\n          >\n            Market Cap\n            {filters.sortBy === \"marketCap\" &&\n              (filters.sortOrder === \"desc\" ? (\n                <TrendingDown className=\"inline w-4 h-4 ml-1\" />\n              ) : (\n                <TrendingUp className=\"inline w-4 h-4 ml-1\" />\n              ))}\n          </button>\n\n          <button\n            onClick={() => handleSortChange(\"change24h\")}\n            className={`px-3 py-2 font-semibold rounded-lg border transition-colors ${\n              filters.sortBy === \"change24h\"\n                ? \"bg-blue-500 text-white border-blue-500\"\n                : \"bg-white text-black border-gray-300 hover:bg-gray-50\"\n            }`}\n          >\n            24h Change\n            {filters.sortBy === \"change24h\" &&\n              (filters.sortOrder === \"desc\" ? (\n                <TrendingDown className=\"inline w-4 h-4 ml-1\" />\n              ) : (\n                <TrendingUp className=\"inline w-4 h-4 ml-1\" />\n              ))}\n          </button>\n\n          <button\n            onClick={() => handleSortChange(\"price\")}\n            className={`px-3 py-2 font-semibold rounded-lg border transition-colors ${\n              filters.sortBy === \"price\"\n                ? \"bg-blue-500 text-white border-blue-500\"\n                : \"bg-white text-black border-gray-300 hover:bg-gray-50\"\n            }`}\n          >\n            Price\n            {filters.sortBy === \"price\" &&\n              (filters.sortOrder === \"desc\" ? (\n                <TrendingDown className=\"inline w-4 h-4 ml-1\" />\n              ) : (\n                <TrendingUp className=\"inline w-4 h-4 ml-1\" />\n              ))}\n          </button>\n        </div>\n      </div>\n\n      {/* Active Filters Display */}\n      {(filters.searchTerm || filters.category) && (\n        <div className=\"mt-4 flex flex-wrap gap-2\">\n          {filters.searchTerm && (\n            <span className=\"px-3 py-1 bg-blue-100 text-blue-900 rounded-full text-sm font-semibold\">\n              Search: &ldquo;{filters.searchTerm}&rdquo;\n              <button\n                onClick={() =>\n                  onFiltersChange({ ...filters, searchTerm: undefined })\n                }\n                className=\"ml-2 text-blue-700 hover:text-blue-900 font-bold\"\n              >\n                ×\n              </button>\n            </span>\n          )}\n          {filters.category && (\n            <span className=\"px-3 py-1 bg-green-100 text-green-900 rounded-full text-sm font-semibold\">\n              Category: {filters.category}\n              <button\n                onClick={() =>\n                  onFiltersChange({ ...filters, category: undefined })\n                }\n                className=\"ml-2 text-green-700 hover:text-green-900 font-bold\"\n              >\n                ×\n              </button>\n            </span>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAHA;;;AAaO,MAAM,kBAAkD,CAAC,EAC9D,OAAO,EACP,eAAe,EACf,UAAU,EACV,UAAU,KAAK,EAChB;IACC,MAAM,qBAAqB,CAAC;QAC1B,gBAAgB;YAAE,GAAG,OAAO;YAAE,YAAY,EAAE,MAAM,CAAC,KAAK;QAAC;IAC3D;IAEA,MAAM,uBAAuB,CAAC;QAC5B,gBAAgB;YACd,GAAG,OAAO;YACV,UAAU,EAAE,MAAM,CAAC,KAAK,KAAK,QAAQ,YAAY,EAAE,MAAM,CAAC,KAAK;QACjE;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,eACJ,QAAQ,MAAM,KAAK,UAAU,QAAQ,SAAS,KAAK,SAC/C,QACA;QACN,gBAAgB;YAAE,GAAG,OAAO;YAAE;YAAQ,WAAW;QAAa;IAChE;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAElB,8OAAC;4BACC,MAAK;4BACL,aAAY;4BACZ,OAAO,QAAQ,UAAU,IAAI;4BAC7B,UAAU;4BACV,WAAU;;;;;;;;;;;;8BAKd,8OAAC;oBACC,OAAO,QAAQ,QAAQ,IAAI;oBAC3B,UAAU;oBACV,WAAU;;sCAEV,8OAAC;4BAAO,OAAM;sCAAM;;;;;;wBACnB,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC;gCAAsB,OAAO;0CAC3B;+BADU;;;;;;;;;;;8BAOjB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS,IAAM,iBAAiB;4BAChC,WAAW,CAAC,iEAAiE,EAC3E,QAAQ,MAAM,KAAK,cACf,2CACA,6EACJ;sCACH;;;;;;sCAID,8OAAC;4BACC,SAAS,IAAM,iBAAiB;4BAChC,WAAW,CAAC,iEAAiE,EAC3E,QAAQ,MAAM,KAAK,cACf,2CACA,6EACJ;sCACH;;;;;;sCAID,8OAAC;4BACC,SAAS,IAAM,iBAAiB;4BAChC,WAAW,CAAC,iEAAiE,EAC3E,QAAQ,MAAM,KAAK,UACf,2CACA,6EACJ;sCACH;;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAElB,8OAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO,QAAQ,UAAU,IAAI;gCAC7B,UAAU;gCACV,WAAU;;;;;;;;;;;;kCAKd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCACC,OAAO,QAAQ,QAAQ,IAAI;gCAC3B,UAAU;gCACV,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAM;;;;;;oCACnB,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC;4CAAsB,OAAO;sDAC3B;2CADU;;;;;;;;;;;;;;;;;kCAQnB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAW,CAAC,4DAA4D,EACtE,QAAQ,MAAM,KAAK,cACf,2CACA,wDACJ;;oCACH;oCAEE,QAAQ,MAAM,KAAK,eAClB,CAAC,QAAQ,SAAS,KAAK,uBACrB,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;6DAExB,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;4CACvB;;;;;;;0CAGL,8OAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAW,CAAC,4DAA4D,EACtE,QAAQ,MAAM,KAAK,cACf,2CACA,wDACJ;;oCACH;oCAEE,QAAQ,MAAM,KAAK,eAClB,CAAC,QAAQ,SAAS,KAAK,uBACrB,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;6DAExB,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;4CACvB;;;;;;;0CAGL,8OAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAW,CAAC,4DAA4D,EACtE,QAAQ,MAAM,KAAK,UACf,2CACA,wDACJ;;oCACH;oCAEE,QAAQ,MAAM,KAAK,WAClB,CAAC,QAAQ,SAAS,KAAK,uBACrB,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;6DAExB,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;4CACvB;;;;;;;;;;;;;;;;;;;YAMR,CAAC,QAAQ,UAAU,IAAI,QAAQ,QAAQ,mBACtC,8OAAC;gBAAI,WAAU;;oBACZ,QAAQ,UAAU,kBACjB,8OAAC;wBAAK,WAAU;;4BAAyE;4BACvE,QAAQ,UAAU;4BAAC;0CACnC,8OAAC;gCACC,SAAS,IACP,gBAAgB;wCAAE,GAAG,OAAO;wCAAE,YAAY;oCAAU;gCAEtD,WAAU;0CACX;;;;;;;;;;;;oBAKJ,QAAQ,QAAQ,kBACf,8OAAC;wBAAK,WAAU;;4BAA2E;4BAC9E,QAAQ,QAAQ;0CAC3B,8OAAC;gCACC,SAAS,IACP,gBAAgB;wCAAE,GAAG,OAAO;wCAAE,UAAU;oCAAU;gCAEpD,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 1097, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/dev/bubblekrc/cryptobubble-clone/src/data/mockCryptoData.ts"], "sourcesContent": ["export interface CryptoCurrency {\n  id: string;\n  name: string;\n  symbol: string;\n  price: number;\n  marketCap: number;\n  volume24h: number;\n  change24h: number;\n  change7d: number;\n  rank: number;\n  logo?: string;\n  description?: string;\n  website?: string;\n  category: string;\n}\n\nexport const mockCryptoData: CryptoCurrency[] = [\n  {\n    id: \"bitcoin\",\n    name: \"Bitcoin\",\n    symbol: \"BTC\",\n    price: 43250.75,\n    marketCap: 847500000000,\n    volume24h: 28500000000,\n    change24h: 2.45,\n    change7d: -1.23,\n    rank: 1,\n    category: \"Layer 1\",\n    description: \"The first and largest cryptocurrency by market cap\"\n  },\n  {\n    id: \"ethereum\",\n    name: \"Ethereum\",\n    symbol: \"ETH\",\n    price: 2650.30,\n    marketCap: 318750000000,\n    volume24h: 15200000000,\n    change24h: 3.78,\n    change7d: 5.42,\n    rank: 2,\n    category: \"Smart Contract Platform\",\n    description: \"Decentralized platform for smart contracts and DApps\"\n  },\n  {\n    id: \"tether\",\n    name: \"Tether\",\n    symbol: \"USDT\",\n    price: 1.00,\n    marketCap: 91800000000,\n    volume24h: 45600000000,\n    change24h: 0.02,\n    change7d: -0.01,\n    rank: 3,\n    category: \"Stablecoin\",\n    description: \"USD-pegged stablecoin\"\n  },\n  {\n    id: \"bnb\",\n    name: \"BNB\",\n    symbol: \"BNB\",\n    price: 315.80,\n    marketCap: 47370000000,\n    volume24h: 1850000000,\n    change24h: -1.25,\n    change7d: 8.90,\n    rank: 4,\n    category: \"Exchange Token\",\n    description: \"Binance ecosystem token\"\n  },\n  {\n    id: \"solana\",\n    name: \"Solana\",\n    symbol: \"SOL\",\n    price: 98.45,\n    marketCap: 43200000000,\n    volume24h: 2100000000,\n    change24h: 5.67,\n    change7d: 12.34,\n    rank: 5,\n    category: \"Layer 1\",\n    description: \"High-performance blockchain for DeFi and Web3\"\n  },\n  {\n    id: \"usdc\",\n    name: \"USD Coin\",\n    symbol: \"USDC\",\n    price: 1.00,\n    marketCap: 32500000000,\n    volume24h: 5800000000,\n    change24h: 0.01,\n    change7d: 0.00,\n    rank: 6,\n    category: \"Stablecoin\",\n    description: \"Regulated USD-backed stablecoin\"\n  },\n  {\n    id: \"xrp\",\n    name: \"XRP\",\n    symbol: \"XRP\",\n    price: 0.62,\n    marketCap: 33800000000,\n    volume24h: 1200000000,\n    change24h: -2.15,\n    change7d: -5.67,\n    rank: 7,\n    category: \"Payment\",\n    description: \"Digital payment protocol for financial institutions\"\n  },\n  {\n    id: \"cardano\",\n    name: \"Cardano\",\n    symbol: \"ADA\",\n    price: 0.48,\n    marketCap: 16900000000,\n    volume24h: 450000000,\n    change24h: 1.89,\n    change7d: 3.45,\n    rank: 8,\n    category: \"Layer 1\",\n    description: \"Proof-of-stake blockchain platform\"\n  },\n  {\n    id: \"avalanche\",\n    name: \"Avalanche\",\n    symbol: \"AVAX\",\n    price: 36.75,\n    marketCap: 14200000000,\n    volume24h: 680000000,\n    change24h: 4.23,\n    change7d: 15.67,\n    rank: 9,\n    category: \"Layer 1\",\n    description: \"Platform for decentralized applications and custom blockchain networks\"\n  },\n  {\n    id: \"dogecoin\",\n    name: \"Dogecoin\",\n    symbol: \"DOGE\",\n    price: 0.085,\n    marketCap: 12100000000,\n    volume24h: 890000000,\n    change24h: -3.45,\n    change7d: 2.10,\n    rank: 10,\n    category: \"Meme\",\n    description: \"The original meme cryptocurrency\"\n  },\n  {\n    id: \"chainlink\",\n    name: \"Chainlink\",\n    symbol: \"LINK\",\n    price: 14.85,\n    marketCap: 8750000000,\n    volume24h: 420000000,\n    change24h: 2.67,\n    change7d: 8.90,\n    rank: 11,\n    category: \"Oracle\",\n    description: \"Decentralized oracle network\"\n  },\n  {\n    id: \"polygon\",\n    name: \"Polygon\",\n    symbol: \"MATIC\",\n    price: 0.89,\n    marketCap: 8200000000,\n    volume24h: 380000000,\n    change24h: 6.78,\n    change7d: 18.45,\n    rank: 12,\n    category: \"Layer 2\",\n    description: \"Ethereum scaling solution\"\n  },\n  {\n    id: \"litecoin\",\n    name: \"Litecoin\",\n    symbol: \"LTC\",\n    price: 72.30,\n    marketCap: 5400000000,\n    volume24h: 320000000,\n    change24h: -1.56,\n    change7d: 4.23,\n    rank: 13,\n    category: \"Payment\",\n    description: \"Peer-to-peer cryptocurrency based on Bitcoin\"\n  },\n  {\n    id: \"uniswap\",\n    name: \"Uniswap\",\n    symbol: \"UNI\",\n    price: 6.45,\n    marketCap: 4850000000,\n    volume24h: 180000000,\n    change24h: 3.89,\n    change7d: 12.67,\n    rank: 14,\n    category: \"DeFi\",\n    description: \"Decentralized exchange protocol\"\n  },\n  {\n    id: \"internet-computer\",\n    name: \"Internet Computer\",\n    symbol: \"ICP\",\n    price: 12.75,\n    marketCap: 5900000000,\n    volume24h: 95000000,\n    change24h: -2.34,\n    change7d: 7.89,\n    rank: 15,\n    category: \"Layer 1\",\n    description: \"Blockchain computer that scales smart contract computation\"\n  }\n];\n\n// Simple seeded random number generator for consistent results\nclass SeededRandom {\n  private seed: number;\n\n  constructor(seed: number) {\n    this.seed = seed;\n  }\n\n  next(): number {\n    this.seed = (this.seed * 9301 + 49297) % 233280;\n    return this.seed / 233280;\n  }\n}\n\n// Helper function to generate additional random crypto data with deterministic values\nexport const generateRandomCrypto = (count: number): CryptoCurrency[] => {\n  const categories = [\"DeFi\", \"Layer 1\", \"Layer 2\", \"Meme\", \"Gaming\", \"NFT\", \"Oracle\", \"Privacy\", \"Storage\"];\n  const cryptoNames = [\n    \"ApeCoin\", \"Shiba Inu\", \"Cosmos\", \"Algorand\", \"VeChain\", \"Filecoin\", \"Sandbox\",\n    \"Decentraland\", \"Axie Infinity\", \"Theta\", \"Hedera\", \"Elrond\", \"Near Protocol\",\n    \"Flow\", \"Tezos\", \"Fantom\", \"Harmony\", \"Zilliqa\", \"Enjin\", \"Basic Attention Token\",\n    \"Compound\", \"Maker\", \"Aave\", \"Curve\", \"SushiSwap\", \"PancakeSwap\", \"1inch\",\n    \"Yearn Finance\", \"Synthetix\", \"Balancer\", \"Bancor\", \"Kyber Network\", \"0x Protocol\"\n  ];\n\n  // Use a fixed seed for consistent results across server and client\n  const rng = new SeededRandom(12345);\n\n  return Array.from({ length: count }, (_, i) => {\n    const basePrice = rng.next() * 1000 + 0.01;\n    const marketCap = basePrice * (rng.next() * 1000000000 + 1000000);\n\n    return {\n      id: `crypto-${i + 16}`,\n      name: cryptoNames[i % cryptoNames.length] || `Crypto ${i + 16}`,\n      symbol: `C${i + 16}`,\n      price: Number(basePrice.toFixed(4)),\n      marketCap: Number(marketCap.toFixed(0)),\n      volume24h: Number((marketCap * (rng.next() * 0.3 + 0.05)).toFixed(0)),\n      change24h: Number(((rng.next() - 0.5) * 20).toFixed(2)),\n      change7d: Number(((rng.next() - 0.5) * 40).toFixed(2)),\n      rank: i + 16,\n      category: categories[Math.floor(rng.next() * categories.length)],\n      description: `Description for ${cryptoNames[i % cryptoNames.length] || `Crypto ${i + 16}`}`\n    };\n  });\n};\n\n// Combine static data with generated data for a total of 50+ cryptocurrencies\nexport const getAllCryptoData = (): CryptoCurrency[] => {\n  return [...mockCryptoData, ...generateRandomCrypto(35)];\n};\n"], "names": [], "mappings": ";;;;;AAgBO,MAAM,iBAAmC;IAC9C;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW;QACX,UAAU,CAAC;QACX,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW;QACX,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW;QACX,UAAU,CAAC;QACX,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW,CAAC;QACZ,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW;QACX,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW;QACX,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW,CAAC;QACZ,UAAU,CAAC;QACX,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW;QACX,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW;QACX,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW,CAAC;QACZ,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW;QACX,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW;QACX,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW,CAAC;QACZ,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW;QACX,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW,CAAC;QACZ,UAAU;QACV,MAAM;QACN,UAAU;QACV,aAAa;IACf;CACD;AAED,+DAA+D;AAC/D,MAAM;IACI,KAAa;IAErB,YAAY,IAAY,CAAE;QACxB,IAAI,CAAC,IAAI,GAAG;IACd;IAEA,OAAe;QACb,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,OAAO,KAAK,IAAI;QACzC,OAAO,IAAI,CAAC,IAAI,GAAG;IACrB;AACF;AAGO,MAAM,uBAAuB,CAAC;IACnC,MAAM,aAAa;QAAC;QAAQ;QAAW;QAAW;QAAQ;QAAU;QAAO;QAAU;QAAW;KAAU;IAC1G,MAAM,cAAc;QAClB;QAAW;QAAa;QAAU;QAAY;QAAW;QAAY;QACrE;QAAgB;QAAiB;QAAS;QAAU;QAAU;QAC9D;QAAQ;QAAS;QAAU;QAAW;QAAW;QAAS;QAC1D;QAAY;QAAS;QAAQ;QAAS;QAAa;QAAe;QAClE;QAAiB;QAAa;QAAY;QAAU;QAAiB;KACtE;IAED,mEAAmE;IACnE,MAAM,MAAM,IAAI,aAAa;IAE7B,OAAO,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAM,GAAG,CAAC,GAAG;QACvC,MAAM,YAAY,IAAI,IAAI,KAAK,OAAO;QACtC,MAAM,YAAY,YAAY,CAAC,IAAI,IAAI,KAAK,aAAa,OAAO;QAEhE,OAAO;YACL,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI;YACtB,MAAM,WAAW,CAAC,IAAI,YAAY,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI;YAC/D,QAAQ,CAAC,CAAC,EAAE,IAAI,IAAI;YACpB,OAAO,OAAO,UAAU,OAAO,CAAC;YAChC,WAAW,OAAO,UAAU,OAAO,CAAC;YACpC,WAAW,OAAO,CAAC,YAAY,CAAC,IAAI,IAAI,KAAK,MAAM,IAAI,CAAC,EAAE,OAAO,CAAC;YAClE,WAAW,OAAO,CAAC,CAAC,IAAI,IAAI,KAAK,GAAG,IAAI,EAAE,EAAE,OAAO,CAAC;YACpD,UAAU,OAAO,CAAC,CAAC,IAAI,IAAI,KAAK,GAAG,IAAI,EAAE,EAAE,OAAO,CAAC;YACnD,MAAM,IAAI;YACV,UAAU,UAAU,CAAC,KAAK,KAAK,CAAC,IAAI,IAAI,KAAK,WAAW,MAAM,EAAE;YAChE,aAAa,CAAC,gBAAgB,EAAE,WAAW,CAAC,IAAI,YAAY,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI,EAAE;QAC7F;IACF;AACF;AAGO,MAAM,mBAAmB;IAC9B,OAAO;WAAI;WAAmB,qBAAqB;KAAI;AACzD", "debugId": null}}, {"offset": {"line": 1391, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/dev/bubblekrc/cryptobubble-clone/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useMemo, useEffect } from \"react\";\nimport { ResponsiveBubbleChart } from \"@/components/ResponsiveBubbleChart\";\nimport { SearchAndFilter } from \"@/components/SearchAndFilter\";\n// CryptoDetailModal removed - details now show on hover\nimport { getAllCryptoData } from \"@/data/mockCryptoData\";\nimport { CryptoCurrency, FilterOptions } from \"@/types\";\n\nexport default function Home() {\n  // Modal state removed - details now show on hover\n  const [isLoading, setIsLoading] = useState(true);\n  const [filters, setFilters] = useState<FilterOptions>({\n    sortBy: \"marketCap\",\n    sortOrder: \"desc\",\n  });\n\n  const allCryptoData = getAllCryptoData();\n\n  // Simulate loading for demo purposes\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setIsLoading(false);\n    }, 1500);\n\n    return () => clearTimeout(timer);\n  }, []);\n\n  // Get unique categories for filter dropdown\n  const categories = useMemo(() => {\n    const uniqueCategories = [\n      ...new Set(allCryptoData.map((crypto) => crypto.category)),\n    ];\n\n    return uniqueCategories.sort();\n  }, [allCryptoData]);\n\n  // Filter and sort data\n  const filteredData = useMemo(() => {\n    let filtered = allCryptoData;\n\n    // Apply search filter\n    if (filters.searchTerm) {\n      const searchLower = filters.searchTerm.toLowerCase();\n      filtered = filtered.filter(\n        (crypto) =>\n          crypto.name.toLowerCase().includes(searchLower) ||\n          crypto.symbol.toLowerCase().includes(searchLower),\n      );\n    }\n\n    // Apply category filter\n    if (filters.category) {\n      filtered = filtered.filter(\n        (crypto) => crypto.category === filters.category,\n      );\n    }\n\n    // Apply sorting\n    if (filters.sortBy) {\n      filtered.sort((a, b) => {\n        const aValue = a[filters.sortBy!];\n        const bValue = b[filters.sortBy!];\n\n        if (typeof aValue === \"string\" && typeof bValue === \"string\") {\n          return filters.sortOrder === \"desc\"\n            ? bValue.localeCompare(aValue)\n            : aValue.localeCompare(bValue);\n        }\n\n        return filters.sortOrder === \"desc\"\n          ? (bValue as number) - (aValue as number)\n          : (aValue as number) - (bValue as number);\n      });\n    }\n\n    return filtered;\n  }, [allCryptoData, filters]);\n\n  // Click handlers removed - details now show on hover\n\n  return (\n    <div className=\"w-full h-screen overflow-hidden\" data-oid=\"nh6aeyy\">\n      {/* Full Screen Bubble Chart */}\n      <ResponsiveBubbleChart\n        data={filteredData}\n        isLoading={isLoading}\n        data-oid=\"e1qxf3h\"\n      />\n\n      {/* Floating Search and Filter Controls */}\n      <div className=\"fixed top-4 left-4 z-50\" data-oid=\"vkdbvt_\">\n        <div\n          className=\"bg-black/20 backdrop-blur-md rounded-lg shadow-2xl p-4 border border-white/30\"\n          style={{\n            boxShadow:\n              \"inset 0 0 20px rgba(255, 255, 255, 0.1), 0 8px 32px rgba(0, 0, 0, 0.3)\",\n          }}\n          data-oid=\"eymeu:z\"\n        >\n          <SearchAndFilter\n            filters={filters}\n            onFiltersChange={setFilters}\n            categories={categories}\n            compact={true}\n            data-oid=\"h7iy:g:\"\n          />\n        </div>\n      </div>\n\n      {/* Floating Stats Panel */}\n      <div className=\"fixed bottom-4 right-4 z-50\" data-oid=\"eq-diib\">\n        <div\n          className=\"bg-black/20 backdrop-blur-md rounded-lg shadow-2xl p-4 border border-white/30 min-w-64\"\n          style={{\n            boxShadow:\n              \"inset 0 0 20px rgba(255, 255, 255, 0.1), 0 8px 32px rgba(0, 0, 0, 0.3)\",\n          }}\n          data-oid=\"ohdqnrj\"\n        >\n          <h3 className=\"font-semibold text-white mb-3\" data-oid=\"n5d9o79\">\n            Market Stats\n          </h3>\n          <div className=\"grid grid-cols-2 gap-3 text-sm\" data-oid=\"_l3_99u\">\n            <div\n              className=\"text-center p-2 bg-green-50 rounded\"\n              data-oid=\"9e3:ppv\"\n            >\n              <div\n                className=\"text-lg font-bold text-green-700\"\n                data-oid=\"i30-cwk\"\n              >\n                {filteredData.filter((c) => c.change24h > 0).length}\n              </div>\n              <div\n                className=\"text-xs text-white font-medium\"\n                data-oid=\"rowh_on\"\n              >\n                Gainers\n              </div>\n            </div>\n            <div\n              className=\"text-center p-2 bg-red-50 rounded\"\n              data-oid=\"x51:-qe\"\n            >\n              <div\n                className=\"text-lg font-bold text-red-700\"\n                data-oid=\"sdgg:_:\"\n              >\n                {filteredData.filter((c) => c.change24h < 0).length}\n              </div>\n              <div\n                className=\"text-xs text-white font-medium\"\n                data-oid=\"zs5q0d4\"\n              >\n                Losers\n              </div>\n            </div>\n          </div>\n          <div\n            className=\"mt-3 text-xs text-white font-medium\"\n            data-oid=\"_c1ulh4\"\n          >\n            <div data-oid=\"9pw2hq4\">\n              Total: {filteredData.length} cryptocurrencies\n            </div>\n            <div data-oid=\"xwn9wh8\">\n              Market Cap: $\n              {(\n                filteredData.reduce((sum, c) => sum + c.marketCap, 0) / 1e12\n              ).toFixed(2)}\n              T\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Floating Legend */}\n      <div className=\"fixed bottom-4 left-4 z-50\" data-oid=\"u:28mnw\">\n        <div\n          className=\"bg-black/20 backdrop-blur-md rounded-lg shadow-2xl p-4 border border-white/30\"\n          style={{\n            boxShadow:\n              \"inset 0 0 20px rgba(255, 255, 255, 0.1), 0 8px 32px rgba(0, 0, 0, 0.3)\",\n          }}\n          data-oid=\"p-ize95\"\n        >\n          <h3\n            className=\"font-semibold text-white mb-3 text-sm\"\n            data-oid=\"66k_ne3\"\n          >\n            Legend\n          </h3>\n          <div className=\"space-y-2 text-xs\" data-oid=\"augmved\">\n            <div className=\"flex items-center gap-2\" data-oid=\"dhb_z-n\">\n              <div\n                className=\"w-3 h-3 bg-green-500 rounded-full\"\n                data-oid=\"my0pfm:\"\n              ></div>\n              <span className=\"text-white\" data-oid=\"m9sb8oj\">\n                Price increase (24h)\n              </span>\n            </div>\n            <div className=\"flex items-center gap-2\" data-oid=\"1uvbjf1\">\n              <div\n                className=\"w-3 h-3 bg-red-500 rounded-full\"\n                data-oid=\"7bwgens\"\n              ></div>\n              <span className=\"text-white\" data-oid=\"lzch3fp\">\n                Price decrease (24h)\n              </span>\n            </div>\n            <div className=\"flex items-center gap-2\" data-oid=\"gnf8q4h\">\n              <div\n                className=\"w-4 h-4 bg-gray-400 rounded-full\"\n                data-oid=\"r9nv:ru\"\n              ></div>\n              <span className=\"text-white\" data-oid=\".sd-nlo\">\n                Bubble size = Market cap\n              </span>\n            </div>\n            <div\n              className=\"pt-2 border-t border-white/30 text-xs text-white\"\n              data-oid=\"3.9phi-\"\n            >\n              <div data-oid=\"298.kho\">• Hover bubbles for details</div>\n              <div data-oid=\"bmts3ij\">• Click bubbles for more info</div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Detail Modal removed - details now show on hover */}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA,wDAAwD;AACxD;AANA;;;;;;AASe,SAAS;IACtB,kDAAkD;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QACpD,QAAQ;QACR,WAAW;IACb;IAEA,MAAM,gBAAgB,CAAA,GAAA,6HAAA,CAAA,mBAAgB,AAAD;IAErC,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,WAAW;YACvB,aAAa;QACf,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG,EAAE;IAEL,4CAA4C;IAC5C,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACzB,MAAM,mBAAmB;eACpB,IAAI,IAAI,cAAc,GAAG,CAAC,CAAC,SAAW,OAAO,QAAQ;SACzD;QAED,OAAO,iBAAiB,IAAI;IAC9B,GAAG;QAAC;KAAc;IAElB,uBAAuB;IACvB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC3B,IAAI,WAAW;QAEf,sBAAsB;QACtB,IAAI,QAAQ,UAAU,EAAE;YACtB,MAAM,cAAc,QAAQ,UAAU,CAAC,WAAW;YAClD,WAAW,SAAS,MAAM,CACxB,CAAC,SACC,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACnC,OAAO,MAAM,CAAC,WAAW,GAAG,QAAQ,CAAC;QAE3C;QAEA,wBAAwB;QACxB,IAAI,QAAQ,QAAQ,EAAE;YACpB,WAAW,SAAS,MAAM,CACxB,CAAC,SAAW,OAAO,QAAQ,KAAK,QAAQ,QAAQ;QAEpD;QAEA,gBAAgB;QAChB,IAAI,QAAQ,MAAM,EAAE;YAClB,SAAS,IAAI,CAAC,CAAC,GAAG;gBAChB,MAAM,SAAS,CAAC,CAAC,QAAQ,MAAM,CAAE;gBACjC,MAAM,SAAS,CAAC,CAAC,QAAQ,MAAM,CAAE;gBAEjC,IAAI,OAAO,WAAW,YAAY,OAAO,WAAW,UAAU;oBAC5D,OAAO,QAAQ,SAAS,KAAK,SACzB,OAAO,aAAa,CAAC,UACrB,OAAO,aAAa,CAAC;gBAC3B;gBAEA,OAAO,QAAQ,SAAS,KAAK,SACzB,AAAC,SAAqB,SACtB,AAAC,SAAqB;YAC5B;QACF;QAEA,OAAO;IACT,GAAG;QAAC;QAAe;KAAQ;IAE3B,qDAAqD;IAErD,qBACE,8OAAC;QAAI,WAAU;QAAkC,YAAS;;0BAExD,8OAAC,2IAAA,CAAA,wBAAqB;gBACpB,MAAM;gBACN,WAAW;gBACX,YAAS;;;;;;0BAIX,8OAAC;gBAAI,WAAU;gBAA0B,YAAS;0BAChD,cAAA,8OAAC;oBACC,WAAU;oBACV,OAAO;wBACL,WACE;oBACJ;oBACA,YAAS;8BAET,cAAA,8OAAC,qIAAA,CAAA,kBAAe;wBACd,SAAS;wBACT,iBAAiB;wBACjB,YAAY;wBACZ,SAAS;wBACT,YAAS;;;;;;;;;;;;;;;;0BAMf,8OAAC;gBAAI,WAAU;gBAA8B,YAAS;0BACpD,cAAA,8OAAC;oBACC,WAAU;oBACV,OAAO;wBACL,WACE;oBACJ;oBACA,YAAS;;sCAET,8OAAC;4BAAG,WAAU;4BAAgC,YAAS;sCAAU;;;;;;sCAGjE,8OAAC;4BAAI,WAAU;4BAAiC,YAAS;;8CACvD,8OAAC;oCACC,WAAU;oCACV,YAAS;;sDAET,8OAAC;4CACC,WAAU;4CACV,YAAS;sDAER,aAAa,MAAM,CAAC,CAAC,IAAM,EAAE,SAAS,GAAG,GAAG,MAAM;;;;;;sDAErD,8OAAC;4CACC,WAAU;4CACV,YAAS;sDACV;;;;;;;;;;;;8CAIH,8OAAC;oCACC,WAAU;oCACV,YAAS;;sDAET,8OAAC;4CACC,WAAU;4CACV,YAAS;sDAER,aAAa,MAAM,CAAC,CAAC,IAAM,EAAE,SAAS,GAAG,GAAG,MAAM;;;;;;sDAErD,8OAAC;4CACC,WAAU;4CACV,YAAS;sDACV;;;;;;;;;;;;;;;;;;sCAKL,8OAAC;4BACC,WAAU;4BACV,YAAS;;8CAET,8OAAC;oCAAI,YAAS;;wCAAU;wCACd,aAAa,MAAM;wCAAC;;;;;;;8CAE9B,8OAAC;oCAAI,YAAS;;wCAAU;wCAErB,CACC,aAAa,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,SAAS,EAAE,KAAK,IAC1D,EAAE,OAAO,CAAC;wCAAG;;;;;;;;;;;;;;;;;;;;;;;;0BAQrB,8OAAC;gBAAI,WAAU;gBAA6B,YAAS;0BACnD,cAAA,8OAAC;oBACC,WAAU;oBACV,OAAO;wBACL,WACE;oBACJ;oBACA,YAAS;;sCAET,8OAAC;4BACC,WAAU;4BACV,YAAS;sCACV;;;;;;sCAGD,8OAAC;4BAAI,WAAU;4BAAoB,YAAS;;8CAC1C,8OAAC;oCAAI,WAAU;oCAA0B,YAAS;;sDAChD,8OAAC;4CACC,WAAU;4CACV,YAAS;;;;;;sDAEX,8OAAC;4CAAK,WAAU;4CAAa,YAAS;sDAAU;;;;;;;;;;;;8CAIlD,8OAAC;oCAAI,WAAU;oCAA0B,YAAS;;sDAChD,8OAAC;4CACC,WAAU;4CACV,YAAS;;;;;;sDAEX,8OAAC;4CAAK,WAAU;4CAAa,YAAS;sDAAU;;;;;;;;;;;;8CAIlD,8OAAC;oCAAI,WAAU;oCAA0B,YAAS;;sDAChD,8OAAC;4CACC,WAAU;4CACV,YAAS;;;;;;sDAEX,8OAAC;4CAAK,WAAU;4CAAa,YAAS;sDAAU;;;;;;;;;;;;8CAIlD,8OAAC;oCACC,WAAU;oCACV,YAAS;;sDAET,8OAAC;4CAAI,YAAS;sDAAU;;;;;;sDACxB,8OAAC;4CAAI,YAAS;sDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStC", "debugId": null}}]}
"use client";

import React, { useState, useMemo, useEffect } from "react";
import { ResponsiveBubbleChart } from "@/components/ResponsiveBubbleChart";
import { SearchAndFilter } from "@/components/SearchAndFilter";
// CryptoDetailModal removed - details now show on hover
import { getAllCryptoData } from "@/data/mockCryptoData";
import { CryptoCurrency, FilterOptions } from "@/types";

export default function Home() {
  // Modal state removed - details now show on hover
  const [isLoading, setIsLoading] = useState(true);
  const [filters, setFilters] = useState<FilterOptions>({
    sortBy: "marketCap",
    sortOrder: "desc",
  });

  const allCryptoData = getAllCryptoData();

  // Simulate loading for demo purposes
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  // Get unique categories for filter dropdown
  const categories = useMemo(() => {
    const uniqueCategories = [
      ...new Set(allCryptoData.map((crypto) => crypto.category)),
    ];

    return uniqueCategories.sort();
  }, [allCryptoData]);

  // Filter and sort data
  const filteredData = useMemo(() => {
    let filtered = allCryptoData;

    // Apply search filter
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      filtered = filtered.filter(
        (crypto) =>
          crypto.name.toLowerCase().includes(searchLower) ||
          crypto.symbol.toLowerCase().includes(searchLower),
      );
    }

    // Apply category filter
    if (filters.category) {
      filtered = filtered.filter(
        (crypto) => crypto.category === filters.category,
      );
    }

    // Apply sorting
    if (filters.sortBy) {
      filtered.sort((a, b) => {
        const aValue = a[filters.sortBy!];
        const bValue = b[filters.sortBy!];

        if (typeof aValue === "string" && typeof bValue === "string") {
          return filters.sortOrder === "desc"
            ? bValue.localeCompare(aValue)
            : aValue.localeCompare(bValue);
        }

        return filters.sortOrder === "desc"
          ? (bValue as number) - (aValue as number)
          : (aValue as number) - (bValue as number);
      });
    }

    return filtered;
  }, [allCryptoData, filters]);

  // Click handlers removed - details now show on hover

  return (
    <div className="w-full h-screen overflow-hidden" data-oid="nh6aeyy">
      {/* Full Screen Bubble Chart */}
      <ResponsiveBubbleChart
        data={filteredData}
        isLoading={isLoading}
        data-oid="e1qxf3h"
      />

      {/* Floating Search and Filter Controls */}
      <div className="fixed top-4 left-4 z-50" data-oid="vkdbvt_">
        <div
          className="bg-black/20 backdrop-blur-md rounded-lg shadow-2xl p-4 border border-white/30"
          style={{
            boxShadow:
              "inset 0 0 20px rgba(255, 255, 255, 0.1), 0 8px 32px rgba(0, 0, 0, 0.3)",
          }}
          data-oid="eymeu:z"
        >
          <SearchAndFilter
            filters={filters}
            onFiltersChange={setFilters}
            categories={categories}
            compact={true}
            data-oid="h7iy:g:"
          />
        </div>
      </div>

      {/* Floating Stats Panel */}
      <div className="fixed bottom-4 right-4 z-50" data-oid="eq-diib">
        <div
          className="bg-black/20 backdrop-blur-md rounded-lg shadow-2xl p-4 border border-white/30 min-w-64"
          style={{
            boxShadow:
              "inset 0 0 20px rgba(255, 255, 255, 0.1), 0 8px 32px rgba(0, 0, 0, 0.3)",
          }}
          data-oid="ohdqnrj"
        >
          <h3 className="font-semibold text-white mb-3" data-oid="n5d9o79">
            Market Stats
          </h3>
          <div className="grid grid-cols-2 gap-3 text-sm" data-oid="_l3_99u">
            <div
              className="text-center p-2 bg-green-50 rounded"
              data-oid="9e3:ppv"
            >
              <div
                className="text-lg font-bold text-green-700"
                data-oid="i30-cwk"
              >
                {filteredData.filter((c) => c.change24h > 0).length}
              </div>
              <div
                className="text-xs text-white font-medium"
                data-oid="rowh_on"
              >
                Gainers
              </div>
            </div>
            <div
              className="text-center p-2 bg-red-50 rounded"
              data-oid="x51:-qe"
            >
              <div
                className="text-lg font-bold text-red-700"
                data-oid="sdgg:_:"
              >
                {filteredData.filter((c) => c.change24h < 0).length}
              </div>
              <div
                className="text-xs text-white font-medium"
                data-oid="zs5q0d4"
              >
                Losers
              </div>
            </div>
          </div>
          <div
            className="mt-3 text-xs text-white font-medium"
            data-oid="_c1ulh4"
          >
            <div data-oid="9pw2hq4">
              Total: {filteredData.length} cryptocurrencies
            </div>
            <div data-oid="xwn9wh8">
              Market Cap: $
              {(
                filteredData.reduce((sum, c) => sum + c.marketCap, 0) / 1e12
              ).toFixed(2)}
              T
            </div>
          </div>
        </div>
      </div>

      {/* Floating Legend */}
      <div className="fixed bottom-4 left-4 z-50" data-oid="u:28mnw">
        <div
          className="bg-black/20 backdrop-blur-md rounded-lg shadow-2xl p-4 border border-white/30"
          style={{
            boxShadow:
              "inset 0 0 20px rgba(255, 255, 255, 0.1), 0 8px 32px rgba(0, 0, 0, 0.3)",
          }}
          data-oid="p-ize95"
        >
          <h3
            className="font-semibold text-white mb-3 text-sm"
            data-oid="66k_ne3"
          >
            Legend
          </h3>
          <div className="space-y-2 text-xs" data-oid="augmved">
            <div className="flex items-center gap-2" data-oid="dhb_z-n">
              <div
                className="w-3 h-3 bg-green-500 rounded-full"
                data-oid="my0pfm:"
              ></div>
              <span className="text-white" data-oid="m9sb8oj">
                Price increase (24h)
              </span>
            </div>
            <div className="flex items-center gap-2" data-oid="1uvbjf1">
              <div
                className="w-3 h-3 bg-red-500 rounded-full"
                data-oid="7bwgens"
              ></div>
              <span className="text-white" data-oid="lzch3fp">
                Price decrease (24h)
              </span>
            </div>
            <div className="flex items-center gap-2" data-oid="gnf8q4h">
              <div
                className="w-4 h-4 bg-gray-400 rounded-full"
                data-oid="r9nv:ru"
              ></div>
              <span className="text-white" data-oid=".sd-nlo">
                Bubble size = Market cap
              </span>
            </div>
            <div
              className="pt-2 border-t border-white/30 text-xs text-white"
              data-oid="3.9phi-"
            >
              <div data-oid="298.kho">• Hover bubbles for details</div>
              <div data-oid="bmts3ij">• Click bubbles for more info</div>
            </div>
          </div>
        </div>
      </div>

      {/* Detail Modal removed - details now show on hover */}
    </div>
  );
}

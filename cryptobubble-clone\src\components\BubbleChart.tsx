"use client";

import React, { useEffect, useRef, useState } from "react";
import * as d3 from "d3";
import { CryptoCurrency, BubbleData } from "@/types";

interface BubbleChartProps {
  data: CryptoCurrency[];
  width?: number;
  height?: number;
  onBubbleHover?: (crypto: CryptoCurrency | null) => void;
}

export const BubbleChart: React.FC<BubbleChartProps> = ({
  data,
  width = 1200,
  height = 800,
  onBubbleHover,
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const [hoveredBubble, setHoveredBubble] = useState<CryptoCurrency | null>(
    null,
  );
  const [pinnedBubble, setPinnedBubble] = useState<CryptoCurrency | null>(
    null,
  );

  useEffect(() => {
    if (!svgRef.current || !data.length) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll("*").remove();

    // Create scales
    const maxMarketCap = d3.max(data, (d) => d.marketCap) || 1;
    const minMarketCap = d3.min(data, (d) => d.marketCap) || 1;

    // Calculate optimal bubble sizes to fill screen without overlapping
    const totalArea = width * height;
    const bubbleCount = data.length;
    const averageArea = (totalArea / bubbleCount) * 0.5; // Use 50% of available space for non-overlapping
    const averageRadius = Math.sqrt(averageArea / Math.PI);

    // Radius scale - conservative sizing to prevent overlap
    const radiusScale = d3
      .scaleSqrt()
      .domain([minMarketCap, maxMarketCap])
      .range([averageRadius * 0.4, averageRadius * 1.8]); // More conservative sizing

    // Color scale for price changes with more vibrant colors
    const colorScale = d3
      .scaleLinear<string>()
      .domain([-15, -5, 0, 5, 15])
      .range(["#dc2626", "#f87171", "#6b7280", "#34d399", "#059669"])
      .clamp(true);

    // Prepare bubble data
    const bubbleData: BubbleData[] = data.map((crypto) => ({
      ...crypto,
      x: 0,
      y: 0,
      r: radiusScale(crypto.marketCap),
      color: colorScale(crypto.change24h),
    }));

    // Create force simulation for non-overlapping bubbles like CryptoBubbles
    const simulation = d3
      .forceSimulation<BubbleData>(bubbleData)
      .force("charge", d3.forceManyBody().strength(-50)) // Moderate repulsion to prevent overlap
      .force("center", d3.forceCenter(width / 2, height / 2))
      .force(
        "collision",
        d3
          .forceCollide<BubbleData>()
          .radius((d) => d.r + 3) // Small padding to prevent overlap
          .strength(1) // Strong collision detection
          .iterations(3),
      ) // Multiple iterations for better collision resolution
      .force("x", d3.forceX<BubbleData>(width / 2).strength(0.05)) // Gentle centering
      .force("y", d3.forceY<BubbleData>(height / 2).strength(0.05))
      // Add boundary forces to keep bubbles on screen
      .force("boundary", () => {
        bubbleData.forEach((d) => {
          const padding = d.r + 5;
          d.x = Math.max(padding, Math.min(width - padding, d.x || width / 2));
          d.y = Math.max(
            padding,
            Math.min(height - padding, d.y || height / 2),
          );
        });
      })
      .alphaDecay(0.005) // Very slow decay for thorough settling
      .velocityDecay(0.6); // Higher friction for stability

    // Create container group
    const container = svg.append("g");

    // Create bubbles with entrance animation
    const bubbles = container
      .selectAll(".bubble")
      .data(bubbleData)
      .enter()
      .append("g")
      .attr("class", "bubble")
      .style("cursor", "pointer")
      .style("opacity", 0);

    // Add circles with gradient effects and inner glow
    const defs = svg.append("defs");

    // Create striking inner glow filter using bubble's own color
    const innerGlowFilter = defs
      .append("filter")
      .attr("id", "inner-glow")
      .attr("x", "-50%")
      .attr("y", "-50%")
      .attr("width", "200%")
      .attr("height", "200%");

    // Step 1: Create inverted alpha mask
    innerGlowFilter
      .append("feComposite")
      .attr("in", "SourceAlpha")
      .attr("in2", "SourceAlpha")
      .attr("operator", "out")
      .attr("result", "inverse");

    // Step 2: Blur the inverted mask for glow effect
    innerGlowFilter
      .append("feGaussianBlur")
      .attr("in", "inverse")
      .attr("stdDeviation", "5")
      .attr("result", "blurred");

    // Step 3: Clip the blur to the original shape for inner glow
    innerGlowFilter
      .append("feComposite")
      .attr("in", "blurred")
      .attr("in2", "SourceAlpha")
      .attr("operator", "in")
      .attr("result", "innerGlow");

    // Step 4: Combine with original for striking effect
    const feMerge = innerGlowFilter.append("feMerge");
    feMerge.append("feMergeNode").attr("in", "SourceGraphic");
    feMerge.append("feMergeNode").attr("in", "innerGlow");

    // Create drop shadow filter
    const shadowFilter = defs
      .append("filter")
      .attr("id", "drop-shadow")
      .attr("x", "-50%")
      .attr("y", "-50%")
      .attr("width", "200%")
      .attr("height", "200%");

    shadowFilter
      .append("feDropShadow")
      .attr("dx", "2")
      .attr("dy", "2")
      .attr("stdDeviation", "4")
      .attr("flood-color", "rgba(0,0,0,0.4)");

    // Create glass-effect gradients matching the reference image
    bubbles.each(function (d, i) {
      const gradient = defs
        .append("radialGradient")
        .attr("id", `gradient-${i}`)
        .attr("cx", "30%")
        .attr("cy", "30%");

      // Create solid, striking bubble effect
      const baseColor = d3.color(d.color);

      // Bright center using the bubble's own color
      gradient
        .append("stop")
        .attr("offset", "0%")
        .attr("stop-color", baseColor?.brighter(1.2)?.toString() || d.color)
        .attr("stop-opacity", "0.6");

      // Transition to main color
      gradient
        .append("stop")
        .attr("offset", "40%")
        .attr("stop-color", baseColor?.brighter(0.5)?.toString() || d.color)
        .attr("stop-opacity", "0.5");

      // Main color area - more solid
      gradient
        .append("stop")
        .attr("offset", "70%")
        .attr("stop-color", d.color)
        .attr("stop-opacity", "0.4");

      // Dark outer edge for strong definition
      gradient
        .append("stop")
        .attr("offset", "100%")
        .attr("stop-color", baseColor?.darker(0.5)?.toString() || d.color)
        .attr("stop-opacity", "0.8");
    });

    // Main transparent bubble with inner glow
    bubbles
      .append("circle")
      .attr("r", 0)
      .attr("fill", (d, i) => `url(#gradient-${i})`)
      .attr("stroke", (d) => {
        const baseColor = d3.color(d.color);
        return baseColor?.brighter(0.5)?.toString() || d.color;
      })
      .attr("stroke-width", 2.5)
      .attr("stroke-opacity", 0.7)
      .attr("opacity", 0.9)
      .attr("filter", "url(#inner-glow)")
      .transition()
      .duration(1000)
      .delay((d, i) => i * 50)
      .attr("r", (d) => d.r)
      .on("end", function () {
        if (this.parentNode) {
          d3.select(this.parentNode as Element).style("opacity", 1);
        }
      });

    // Enhanced hover effects with proper transform handling
    bubbles
      .on("mouseover", function (event, d) {
        const circle = d3.select(this).select("circle");

        // Cancel any ongoing transitions to prevent conflicts
        circle.interrupt();

        // Store the current scale for this bubble
        d.hoverScale = 1.1;

        // Enhance transparency and glow on hover
        circle
          .transition()
          .duration(200)
          .attr("opacity", 1)
          .attr("stroke-width", 3)
          .attr("stroke", "#fbbf24")
          .attr("stroke-opacity", 0.8);

        // Bring to front by moving to end of parent (proper SVG z-ordering)
        const parent = this.parentNode;
        if (parent) {
          parent.appendChild(this);
        }

        setHoveredBubble(d);
        onBubbleHover?.(d);
      })
      .on("mouseout", function (event, d) {
        // Don't reset hover effects if this bubble is pinned
        if (pinnedBubble?.id === d.id) return;

        const circle = d3.select(this).select("circle");

        // Cancel any ongoing transitions to prevent conflicts
        circle.interrupt();

        // Reset the scale for this bubble
        d.hoverScale = 1;

        // Reset to transparent state
        circle
          .transition()
          .duration(200)
          .attr("opacity", 0.9)
          .attr("stroke-width", 2)
          .attr("stroke", function () {
            const baseColor = d3.color(d.color);
            return baseColor?.brighter(0.5)?.toString() || d.color;
          })
          .attr("stroke-opacity", 0.7);

        // Only clear hover if no bubble is pinned
        if (!pinnedBubble) {
          setHoveredBubble(null);
          onBubbleHover?.(null);
        }
      })
      .on("click", function (event, d) {
        // Toggle pin state
        if (pinnedBubble?.id === d.id) {
          // Unpin if clicking the same bubble
          setPinnedBubble(null);
          setHoveredBubble(null);
          onBubbleHover?.(null);

          // Reset visual state
          const circle = d3.select(this).select("circle");
          circle.interrupt();
          d.hoverScale = 1;
          circle
            .transition()
            .duration(200)
            .attr("opacity", 0.9)
            .attr("stroke-width", 2)
            .attr("stroke", function () {
              const baseColor = d3.color(d.color);
              return baseColor?.brighter(0.5)?.toString() || d.color;
            })
            .attr("stroke-opacity", 0.7);
        } else {
          // Pin this bubble
          setPinnedBubble(d);
          setHoveredBubble(d);
          onBubbleHover?.(d);

          // Ensure visual state is active with pinned styling
          const circle = d3.select(this).select("circle");
          circle.interrupt();
          d.hoverScale = 1.1;
          circle
            .transition()
            .duration(200)
            .attr("opacity", 1)
            .attr("stroke-width", 4)
            .attr("stroke", "#fbbf24")
            .attr("stroke-opacity", 1);
        }
      })

    // Add symbol text - show for all bubbles with adaptive sizing
    bubbles
      .append("text")
      .attr("text-anchor", "middle")
      .attr("dy", "-0.1em")
      .attr("font-size", (d) => Math.max(8, Math.min(d.r * 0.4, 20)))
      .attr("font-weight", "bold")
      .attr("fill", "#ffffff")
      .attr("pointer-events", "none")
      .style("text-shadow", "2px 2px 4px rgba(0,0,0,0.9)")
      .style("font-family", "system-ui, -apple-system, sans-serif")
      .style("dominant-baseline", "central")
      .text((d) => d.symbol);

    // Add percentage text - show for all bubbles
    bubbles
      .append("text")
      .attr("text-anchor", "middle")
      .attr("dy", "0.8em")
      .attr("font-size", (d) => Math.max(6, Math.min(d.r * 0.3, 16)))
      .attr("font-weight", "600")
      .attr("fill", "#ffffff")
      .attr("pointer-events", "none")
      .style("text-shadow", "2px 2px 4px rgba(0,0,0,0.9)")
      .style("font-family", "system-ui, -apple-system, sans-serif")
      .style("dominant-baseline", "central")
      .text((d) => `${d.change24h >= 0 ? "+" : ""}${d.change24h.toFixed(2)}%`);

    // Run simulation for enough iterations to settle bubbles without overlap
    for (let i = 0; i < 500; i++) {
      simulation.tick();
    }

    // Update positions on simulation tick with proper scaling
    simulation.on("tick", () => {
      bubbles.attr("transform", (d) => {
        const scale = d.clickScale || d.hoverScale || 1;
        return `translate(${d.x},${d.y}) scale(${scale})`;
      });
    });

    // Remove zoom functionality - bubbles should fill the screen

    // Cleanup
    return () => {
      simulation.stop();
    };
  }, [data, width, height, onBubbleHover]);

  return (
    <div className="relative" data-oid="cn3_xst">
      <svg
        ref={svgRef}
        width={width}
        height={height}
        className="bg-gradient-to-br from-gray-900 to-gray-800"
        style={{ display: "block" }}
        data-oid="vyfe8jc"
        onClick={(e) => {
          // Unpin bubble when clicking on background
          if (e.target === svgRef.current) {
            setPinnedBubble(null);
            setHoveredBubble(null);
            onBubbleHover?.(null);
          }
        }}
      />

      {/* Enhanced Tooltip */}
      {(hoveredBubble || pinnedBubble) && (() => {
        const displayBubble = hoveredBubble || pinnedBubble;
        const isPinned = pinnedBubble?.id === displayBubble?.id;

        return (
          <div
            className={`absolute top-4 right-4 bg-black/20 p-5 rounded-xl shadow-2xl border-2 z-20 min-w-72 max-w-80 backdrop-blur-md ${
              isPinned ? 'border-yellow-400/50' : 'border-white/30'
            }`}
            style={{
              boxShadow:
                "inset 0 0 20px rgba(255, 255, 255, 0.1), 0 8px 32px rgba(0, 0, 0, 0.3)",
            }}
            data-oid="km3npxp"
          >
            {isPinned && (
              <div className="absolute -top-2 -right-2 w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center">
                <span className="text-black text-xs font-bold">📌</span>
              </div>
            )}
            <div className="flex items-center gap-3 mb-3" data-oid="jc0k_nz">
              <div
                className="w-10 h-10 rounded-full flex items-center justify-center text-white font-bold text-sm"
                style={{
                  backgroundColor:
                    displayBubble.change24h >= 0 ? "#22c55e" : "#ef4444",
                }}
                data-oid="dejxve:"
              >
                {displayBubble.symbol.charAt(0)}
              </div>
              <div data-oid="1ok57-r">
                <h3 className="font-bold text-lg text-white" data-oid="wmdqdov">
                  {displayBubble.name}
                </h3>
                <span
                  className="text-white/80 text-sm font-medium"
                  data-oid="evx3o28"
                >
                  ({displayBubble.symbol}) • Rank #{displayBubble.rank}
                </span>
              </div>
            </div>

            <div className="space-y-2 text-sm" data-oid="h0nfuhh">
              <div
                className="flex justify-between items-center"
                data-oid="d6yjrk9"
              >
                <span className="text-white/90 font-medium" data-oid="6m.58cj">
                  Price:
                </span>
                <span className="font-bold text-lg text-white" data-oid="0bj4iet">
                  $
                  {displayBubble.price < 1
                    ? displayBubble.price.toFixed(4)
                    : displayBubble.price.toLocaleString()}
                </span>
              </div>

              <div
                className="flex justify-between items-center"
                data-oid="07x2:ab"
              >
                <span className="text-white/90 font-medium" data-oid="ot1y:3s">
                  Market Cap:
                </span>
                <span className="font-semibold text-white" data-oid="t5x.htn">
                  {displayBubble.marketCap >= 1e9
                    ? `$${(displayBubble.marketCap / 1e9).toFixed(2)}B`
                    : `$${(displayBubble.marketCap / 1e6).toFixed(2)}M`}
                </span>
              </div>

              <div
                className="flex justify-between items-center"
                data-oid="hpxbbcg"
              >
                <span className="text-white/90 font-medium" data-oid="4myuzet">
                  24h Volume:
                </span>
                <span className="font-semibold text-white" data-oid="pbh7rf.">
                  {displayBubble.volume24h >= 1e9
                    ? `$${(displayBubble.volume24h / 1e9).toFixed(2)}B`
                    : `$${(displayBubble.volume24h / 1e6).toFixed(2)}M`}
                </span>
              </div>

              <div
                className="flex justify-between items-center"
                data-oid="rli2gdt"
              >
                <span className="text-white/90 font-medium" data-oid="_87x9tp">
                  24h Change:
                </span>
                <div
                  className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs font-bold ${
                    displayBubble.change24h >= 0
                      ? "bg-green-500/20 text-green-300 border border-green-400/30"
                      : "bg-red-500/20 text-red-300 border border-red-400/30"
                  }`}
                  data-oid="wxq.s6:"
                >
                  <span data-oid="rdhy:ha">
                    {displayBubble.change24h >= 0 ? "↗" : "↘"}
                  </span>
                  <span data-oid="f-r8.4q">
                    {displayBubble.change24h >= 0 ? "+" : ""}
                    {displayBubble.change24h.toFixed(2)}%
                  </span>
                </div>
              </div>

              <div
                className="flex justify-between items-center"
                data-oid="a4w:gre"
              >
                <span className="text-white/90 font-medium" data-oid="n46j1tl">
                  7d Change:
                </span>
                <span
                  className={`font-semibold ${displayBubble.change7d >= 0 ? "text-green-300" : "text-red-300"}`}
                  data-oid="8cu:u_m"
                >
                  {displayBubble.change7d >= 0 ? "+" : ""}
                  {displayBubble.change7d.toFixed(2)}%
                </span>
              </div>

              <div className="pt-2 border-t border-white/30" data-oid="_ul03a5">
                <div
                  className="flex justify-between items-center"
                  data-oid="yrv.:ln"
                >
                  <span className="text-white/90 font-medium" data-oid="mwc:cg_">
                    Category:
                  </span>
                  <span
                    className="px-2 py-1 bg-blue-500/20 text-blue-300 border border-blue-400/30 rounded-full text-xs font-bold"
                    data-oid="y9j.jp4"
                  >
                    {displayBubble.category}
                  </span>
                </div>
              </div>
            </div>

            <div
              className="mt-3 pt-3 border-t border-white/30 text-xs text-white/80 text-center font-medium"
              data-oid="ofbq3_c"
            >
              {isPinned ? 'Click to unpin • Drag to move' : 'Click to pin • Drag to move'}
            </div>
          </div>
        );
      })()}
    </div>
  );
};
